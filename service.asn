--
-- Types often found in programming languages.
--

ServiceManagementHTTP DEFINITIONS AUTOMATIC TAGS ::=

BEGIN

Int8 ::= INTEGER (-128..127)

Int16 ::= INTEGER (-32768..32767)

Int32 ::= INTEGER (-2147483648..2147483647)

Int64 ::= INTEGER (-9223372036854775808..9223372036854775807)

Uint8 ::= INTEGER (0..255)

Uint16 ::= INTEGER (0..65535)

Uint32 ::= INTEGER (0..4294967295)

Uint64 ::= INTEGER (0..18446744073709551615)

-- Single-precision (32 bits) floating point number, also known as
-- binary32.
Float ::= REAL (WITH COMPONENTS {
                    mantissa (-16777215..16777215),
                    base (2),
                    exponent (-149..104)
                })

-- Double-precision (64 bits) floating point number, also known as
-- binary64.
Double ::= REAL (WITH COMPONENTS {
                     mantissa (-9007199254740991..9007199254740991),
                     base (2),
                     exponent (-1074..971)
                 })

Bool ::= BOOLEAN

-- UTF-8 or 7-bit ASCII
String ::= UTF8String

Bytes ::= OCTET STRING

MessageRequestFrame ::= SEQUENCE {
    version     Uint8(1),
    content     CHOICE {
        setInterfaceIpRequest       SetInterfaceIpRequest,
        serviceConfigRequest          ServiceConfigRequest,
        serviceControlRequest         ServiceControlRequest,
        serviceStatusQueryRequest     ServiceStatusQueryRequest,
        serviceConfigQueryRequest     ServiceConfigQueryRequest,
        alarmReportRequest           AlarmReportRequest,
        workStatusRequest            WorkStatusRequest,
        getAllServiceIdsRequest		GetAllServiceIdsRequest,
        sendPacketStatsRequest 		SendPacketStatsRequest,
        receivePacketStatsRequest	ReceivePacketStatsRequest,
        checkCommStatusRequest  	CheckCommStatusRequest,
        queryHostMngRequest         QueryHostMngRequest,
        setHostMngRequest          SetHostMngRequest,
        querySourceDeviceRequest   QuerySourceDeviceRequest,
        setSourceDeviceRequest     SetSourceDeviceRequest,
				queryRouteListRequest      QueryRouteListRequest,
				queryCurRouteRequest       QueryCurRouteRequest,
				setRouteListRequest        SetRouteListRequest,
        ...
    }
}

MessageResponseFrame ::= SEQUENCE {
    version     Uint8(1),
    content     CHOICE {
        setInterfaceIpResponse      SetInterfaceIpResponse,
        serviceConfigResponse         ServiceConfigResponse,
        serviceControlResponse        ServiceControlResponse,
        serviceStatusQueryResponse    ServiceStatusQueryResponse,
        serviceConfigQueryResponse    ServiceConfigQueryResponse,
        alarmReportResponse          AlarmReportResponse,
        workStatusResponse           WorkStatusResponse,
        getAllServiceIdsResponse 	GetAllServiceIdsResponse,
        sendPacketStatsResponse		SendPacketStatsResponse,
        receivePacketStatsResponse 	ReceivePacketStatsResponse,
        checkCommStatusResponse 	CheckCommStatusResponse,
        queryHostMngResponse        QueryHostMngResponse,
        setHostMngResponse          SetHostMngResponse,
        querySourceDeviceResponse   QuerySourceDeviceResponse,
        setSourceDeviceResponse     SetSourceDeviceResponse,
				queryRouteListResponse      QueryRouteListResponse,
				queryCurRouteResponse       QueryCurRouteResponse,
				setRouteListResponse        SetRouteListResponse,
        error                        ErrorResponse,
        ...
    }
}

SetInterfaceIpRequest ::= SEQUENCE {
    messageType    ContentMessageType,
    interfaceType  InterfaceType,
    ipAddress      IPAddress,
    subnetMask     IPAddress,
    gateway        IPAddress OPTIONAL
}

SetInterfaceIpResponse ::= SEQUENCE {
    messageType       ContentMessageType,
    interfaceType     InterfaceType,
    currentIpAddress  IPAddress,
    currentSubnetMask IPAddress,
    currentGateway    IPAddress OPTIONAL,
    result            INTEGER (0..1000)
}

ServiceConfigRequest ::= CHOICE {
    addServiceRequest        AddServiceRequest,
    updateServiceRequest     UpdateServiceRequest,
    deleteServiceRequest     DeleteServiceRequest,
    ...
}

ServiceConfigResponse ::= SEQUENCE {
    messageType     ContentMessageType,
    serviceId       ServiceId,
    ...
}

ServiceControlRequest ::= SEQUENCE {
    messageType			ContentMessageType,
    serviceId       	ServiceId,
    serviceStartOrStop	ServiceControlAction,
    ...
}

ServiceControlResponse ::= SEQUENCE {
    messageType 		ContentMessageType,
    serviceId       	ServiceId,
    serviceStartOrStop 	ServiceControlAction,
    ...
}

ServiceStatusQueryRequest ::= SEQUENCE {
    messageType     ContentMessageType,
    serviceId       ServiceId,
    ...
}

ServiceStatusQueryResponse ::= SEQUENCE {
    messageType     ContentMessageType,
    serviceId       ServiceId,
    serviceStatus   ServiceStatus,
    ...
}

ServiceConfigQueryRequest ::= SEQUENCE {
    messageType     ContentMessageType,
    serviceId       ServiceId,
    network         Network,
    ...
}

ServiceConfigQueryResponse ::= SEQUENCE {
    messageType         ContentMessageType,
    serviceId           ServiceId,
    displayname         DisplayName,
    network             Network,
    proxyIp             IPAddress OPTIONAL,
    proxyPort           PortNumber OPTIONAL,
    serverIp            IPAddress OPTIONAL,
    serverPort          PortNumber OPTIONAL,
    contentKeyCheck     ContentKeyCheck OPTIONAL,
    protocolFilter      ProtocolFilter OPTIONAL,
    unpassDeal          PermissionState OPTIONAL,
    ...
}

AlarmReportRequest ::= SEQUENCE {
    messageType     ContentMessageType,
    serviceId       ServiceId,
    alarmType       AlarmType,
    alarmCode       AlarmCode,
    alarmStatus     AlarmStatus,
    happenTime      Uint64 OPTIONAL,
    resolvedTime    Uint64 OPTIONAL,
    alarmDesc       IA5String (SIZE(0..200)) OPTIONAL,
    ...
}

AlarmReportResponse ::= SEQUENCE {
    messageType     ContentMessageType,
    serviceId       ServiceId,
    ...
}

WorkStatusRequest ::= SEQUENCE {
    messageType     ContentMessageType,
    serviceId       ServiceId,
    requestTime     Uint64 OPTIONAL,
    ...
}

WorkStatusResponse ::= SEQUENCE {
    messageType         ContentMessageType,
    serviceId           ServiceId,
    sendPkgNumToday     Uint32,
    sendPkgSizeToday    Uint32,
    recvPkgNumToday     Uint32,
    recvPkgSizeToday    Uint32,
    devTime             Uint64 OPTIONAL,
    ...
}

GetAllServiceIdsRequest ::= SEQUENCE {
    messageType     ContentMessageType
}

GetAllServiceIdsResponse ::= SEQUENCE {
    messageType     ContentMessageType,
    serviceIds      SEQUENCE OF ServiceId
}

SendPacketStatsRequest ::= SEQUENCE {
    messageType     ContentMessageType,
    serviceId       ServiceId,
    network         Network,
    period          Uint32 OPTIONAL
}

SendPacketStatsResponse ::= SEQUENCE {
    messageType     ContentMessageType,
    serviceId       ServiceId,
    network         Network,
    period          Uint32 OPTIONAL,
    packetStats     SEQUENCE OF PacketStats
}

ReceivePacketStatsRequest ::= SEQUENCE {
    messageType     ContentMessageType,
    serviceId       ServiceId,
    network         Network,
    period          Uint32 OPTIONAL
}

ReceivePacketStatsResponse ::= SEQUENCE {
    messageType     ContentMessageType,
    serviceId       ServiceId,
    network         Network,
    period          Uint32 OPTIONAL,
    packetStats     SEQUENCE OF PacketStats
}

CheckCommStatusRequest ::= SEQUENCE {
    messageType     ContentMessageType,
    serviceId       ServiceId,
    network         Network
}

CheckCommStatusResponse ::= SEQUENCE {
    messageType       ContentMessageType,
    serviceId         ServiceId,
    network           Network,
    isConnected       BOOLEAN,
    connectionEventTime    Uint64
}

QueryHostMngRequest::= SEQUENCE {
    messageType     ContentMessageType
}

QueryHostMngResponse ::= SEQUENCE {
    messageType         ContentMessageType,
    centerIP            IPAddress,
    authPort            PortNumber,
    alarmPort           PortNumber,
    certMngPort         PortNumber,
    sgPort              PortNumber
}

SetHostMngRequest ::= SEQUENCE {
    messageType         ContentMessageType,
    centerIP            IPAddress OPTIONAL,
    authPort            PortNumber OPTIONAL,
    alarmPort           PortNumber OPTIONAL,
    certMngPort         PortNumber OPTIONAL,
    sgPort              PortNumber OPTIONAL
}

SetHostMngResponse::= SEQUENCE {
    messageType        ContentMessageType,
		centerIP           IPAddress,
    authPort           PortNumber,
    alarmPort          PortNumber,
    certMngPort        PortNumber,
    sgPort             PortNumber,
		result             INTEGER (0..1000)
}

QuerySourceDeviceRequest ::= SEQUENCE {
    messageType       ContentMessageType,
    serviceId         ServiceId
}

QuerySourceDeviceResponse ::= SEQUENCE {
    messageType    ContentMessageType,
    serviceId      ServiceId,
    currentDevices SEQUENCE OF SourceDevice
}

SetSourceDeviceRequest ::= SEQUENCE {
    messageType       ContentMessageType,
    serviceId         ServiceId,
    sourcedevices     SEQUENCE OF SourceDevice
}

SetSourceDeviceResponse ::= SEQUENCE {
    messageType    ContentMessageType,
    serviceId      ServiceId,
    currentDevices SEQUENCE OF SourceDevice,
    result         INTEGER (0..1000)
}

QueryRouteListRequest ::= SEQUENCE {
    messageType    ContentMessageType,
    interfaceType  InterfaceType
}

QueryRouteListResponse ::= SEQUENCE {
    messageType    ContentMessageType,
    interfaceType  InterfaceType,
    routeList      SEQUENCE OF RouteList,
    result         INTEGER (0..1000)
}

QueryCurRouteRequest ::= SEQUENCE {
    messageType    ContentMessageType,
    interfaceType  InterfaceType
}

QueryCurRouteResponse ::= SEQUENCE {
    messageType    ContentMessageType,
		interfaceType  InterfaceType,
    routeList      SEQUENCE OF RouteList,
		result         INTEGER (0..1000)
}

SetRouteListRequest ::= SEQUENCE {
    messageType    ContentMessageType,
    interfaceType  InterfaceType,
    routeList      SEQUENCE OF RouteList
}

SetRouteListResponse ::= SEQUENCE {
    messageType    ContentMessageType,
    interfaceType  InterfaceType,
    routeList      SEQUENCE OF RouteList,
    result         INTEGER (0..1000)
}

ErrorResponse ::= SEQUENCE {
    messageType     ContentMessageType,
    errorState      ProcessErrorState,
    ...
}

ContentMessageType ::= ENUMERATED {
    setInterfaceIpService(0),
    addService(1),
    updateService(2),
    deleteService(3),
    controlService(4),
    queryServiceStatus(5),
    queryServiceConfig(6),
    reportAlarm(7),
    queryWorkStatus(8),
    getAllServiceIds(9),
    sendPacketStats(10),
    receivePacketStats(11),
    checkCommStatusService(12),
    hostMngService(13),
    sourceDeviceService(14),
    iprouteService(15),
    ...
}

ProcessErrorState ::= ENUMERATED {
    messageStructureError(0),
    serviceNumLimitError(1),
    displayNameConflictError(2),
    illegalArgumentError(3),
    serviceStatusError(4),
    serviceNotExistError(5),
    illegalOperationError(6),
    sessionTimeout(7),
    ...
}

AddServiceRequest ::= SEQUENCE {
    messageType         ContentMessageType,
    displayname         DisplayName,
    network             Network,
    proxyIp             IPAddress OPTIONAL,
    proxyPort           PortNumber OPTIONAL,
    serverIp            IPAddress OPTIONAL,
    serverPort          PortNumber OPTIONAL,
    contentKeyCheck     ContentKeyCheck OPTIONAL,
    protocolFilter      ProtocolFilter OPTIONAL,
    unpassDeal          PermissionState OPTIONAL,
    ...
}

UpdateServiceRequest ::= SEQUENCE {
    messageType         ContentMessageType,
    serviceId           ServiceId,
    network             Network,
    proxyIp             IPAddress OPTIONAL,
    proxyPort           PortNumber OPTIONAL,
    serverIp            IPAddress OPTIONAL,
    serverPort          PortNumber OPTIONAL,
    contentKeyCheck     ContentKeyCheck OPTIONAL,
    protocolFilter      ProtocolFilter OPTIONAL,
    unpassDeal          PermissionState OPTIONAL,
    ...
}

DeleteServiceRequest ::= SEQUENCE {
    messageType     ContentMessageType,
    serviceId       ServiceId,
    ...
}

-- Missing definition
ServiceStatus ::= ENUMERATED {
    running(0),    --已启动
    stopped(1),    --已停止
    ...
}

ServiceId ::= INTEGER (0..32767)

IPAddress ::= CHOICE {
    ipV4    IPv4Address,
    ipV6    IPv6Address
}

IPv4Address ::= OCTET STRING (SIZE(4))

IPv6Address ::= OCTET STRING (SIZE(16))

PortNumber ::= INTEGER (1025..65535)

DisplayName ::= OCTET STRING (SIZE (1..40))

Network ::= ENUMERATED {
    sender(0),    -- 发送端
    receiver(1),  -- 接收端
    ...
}

ProtocolFilter ::= BIT STRING {
		-- 按位定义，每位取值1：开启，0：关闭:
    crcfilter(0),    --crc16格式过滤
    asnfilter(1)     --asn格式过滤
    -- Bits 2~7 reserved
} (SIZE(8,...))

ContentKeyCheck::= BIT STRING {
		-- 按位定义，每位取值1：开启，0：关闭:
    carcheck(0),    --机动车牌效验
    idcheck(1)      --cn身份证效验
    -- Bits 2~7 reserved
} (SIZE(8,...))

PermissionState ::= ENUMERATED {
    allow(0),     --表示仅报警数据正常传输
    forbidden(1), --表示报警+数据丢弃不转发
    ...
}

ServiceControlAction ::= ENUMERATED {
    start(0),     --启动服务
    stop(1),      --停止服务
    ...
}

AlarmType ::= ENUMERATED {
    faultAlarm(1),            --故障报警（可以自动恢复解除）
    securityAlarm(2),         --安全告警（非故障提示性告警）
    ...
}

AlarmCode ::= ENUMERATED {
    illegalCertificate(1),    --非许可身份连接（证书非法）
    deviceException(2),       --设备异常
    channelException(3),      --通道连接异常
    protocolVerifyFailure(4), --协议效验失败
    keywordCheckFailure(5),   --关键字校验失败
    ...
}

AlarmStatus ::= ENUMERATED {
    alarmRaised(0),           --报警
    alarmCleared(1),          --报警解除
    ...
}

PacketType ::= ENUMERATED{
    cim(1),
    slgs(2),
    lcs(3),
    bsm(4),
    other(9),
    ...
}

PacketStats ::= SEQUENCE {
    packetType   	PacketType,   -- 包类型
    packetCount    	Uint32        -- 该类型包的数量
}

InterfaceType ::= ENUMERATED {
    management,    -- 管理口
    business      -- 业务口
}

SourceDevice ::= SEQUENCE {
    ipAddress IPAddress,   -- 源设备IP地址
    port      INTEGER  OPTIONAL    -- 源设备端口号
}


RouteList ::= SEQUENCE {
    nextIpAddr  IPAddress,
    subnetMask  IPAddress,
    gateway     IPAddress,
    metric      INTEGER
}
END