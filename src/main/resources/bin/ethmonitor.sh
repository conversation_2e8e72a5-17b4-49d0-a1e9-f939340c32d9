#!/bin/bash

INTERFACE="eth1"
LOGFILE="/var/log/eth1_link.log"
MAX_LOG_SIZE="2M"  # 最大日志文件大小
MAX_LOG_FILES=2     # 保留的历史日志文件数量

# 检查并轮转日志文件的函数
rotate_log() {
    if [ -f "$LOGFILE" ]; then
        # 检查文件大小 (转换为字节)
        local max_size_bytes
        case $MAX_LOG_SIZE in
            *K|*k) max_size_bytes=$((${MAX_LOG_SIZE%[Kk]} * 1024)) ;;
            *M|*m) max_size_bytes=$((${MAX_LOG_SIZE%[Mm]} * 1024 * 1024)) ;;
            *G|*g) max_size_bytes=$((${MAX_LOG_SIZE%[Gg]} * 1024 * 1024 * 1024)) ;;
            *) max_size_bytes=$MAX_LOG_SIZE ;;
        esac
        
        local current_size=$(stat -f%z "$LOGFILE" 2>/dev/null || stat -c%s "$LOGFILE" 2>/dev/null || echo 0)
        
        if [ "$current_size" -gt "$max_size_bytes" ]; then
            # 轮转日志文件
            for i in $(seq $((MAX_LOG_FILES-1)) -1 1); do
                [ -f "${LOGFILE}.$i" ] && mv "${LOGFILE}.$i" "${LOGFILE}.$((i+1))"
            done
            [ -f "$LOGFILE" ] && mv "$LOGFILE" "${LOGFILE}.1"
            
            # 删除过老的日志文件
            [ -f "${LOGFILE}.$((MAX_LOG_FILES+1))" ] && rm -f "${LOGFILE}.$((MAX_LOG_FILES+1))"
        fi
    fi
}

# 写入日志的函数
write_log() {
    local message="$1"
    rotate_log
    echo "$(date "+%Y-%m-%d %H:%M:%S") $message" >> "$LOGFILE"
}

ip monitor link dev $INTERFACE | while read -r line; do
    if echo "$line" | grep -q "state UP"; then
        write_log "$INTERFACE LINK UP"
    elif echo "$line" | grep -q "state DOWN"; then
        write_log "$INTERFACE LINK DOWN"
    fi
done
