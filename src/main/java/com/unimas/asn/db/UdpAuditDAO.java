package com.unimas.asn.db;

import com.unimas.asn.servicemanager.servicemanagementhttp.PacketStats;
import com.unimas.asn.servicemanager.servicemanagementhttp.PacketType;

import com.unimas.asn.servicemanager.servicemanagementhttp.Uint32;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Data Access Object for udp_audit table
 */
public class UdpAuditDAO {
    private static final Logger logger = LoggerFactory.getLogger(UdpAuditDAO.class);

    private static final String SELECT_PACKET_STATS_BY_SERVICE =
            "SELECT pack_type, SUM(count) as total_count " +
            "FROM udp_audit " +
            "WHERE serviceid = ? " +
            "GROUP BY pack_type " +
            "ORDER BY pack_type";
    private static final String SELECT_SEND_PACKET_STATS_BY_SERVICE_AND_PERIOD =
            "SELECT pack_type, SUM(count) as total_count " +
                    "FROM udp_audit " +
                    "WHERE serviceid = ? AND begintime >= DATE_SUB(NOW(), INTERVAL ? MINUTE) " +
                    "GROUP BY pack_type " +
                    "ORDER BY pack_type";
    private static final String SELECT_RECV_PACKET_STATS_BY_SERVICE_AND_PERIOD =
            "SELECT pack_type, SUM(count_r) as total_count " +
            "FROM udp_audit " +
            "WHERE serviceid = ? AND begintime >= DATE_SUB(NOW(), INTERVAL ? MINUTE) " +
            "GROUP BY pack_type " +
            "ORDER BY pack_type";

    // 新增查询语句：获取发送包统计数据（基于count字段为1且当天的记录）
    private static final String SELECT_SEND_STATS_BY_SERVICE =
            "SELECT SUM(count) as total_count, SUM(bytes) as total_bytes " +
            "FROM udp_audit " +
            "WHERE serviceid = ? AND count = 1 AND begintime >= CURDATE() AND begintime < DATE_ADD(CURDATE(), INTERVAL 1 DAY)";

    // 新增查询语句：获取接收包统计数据（基于count_r字段为1且当天的记录）
    private static final String SELECT_RECV_STATS_BY_SERVICE =
            "SELECT SUM(count_r) as total_count, SUM(bytes) as total_bytes " +
            "FROM udp_audit " +
            "WHERE serviceid = ? AND count_r = 1 AND begintime >= CURDATE() AND begintime < DATE_ADD(CURDATE(), INTERVAL 1 DAY)";

    /**
     * 获取指定服务的包统计数据
     * @param serviceId 服务ID
     * @return 包统计数据列表
     */
    public List<PacketStats> getPacketStatsByServiceId(String serviceId) {
        List<PacketStats> packetStatsList = new ArrayList<>();

        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement stmt = conn.prepareStatement(SELECT_PACKET_STATS_BY_SERVICE)) {
            
            stmt.setString(1, serviceId);
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    PacketStats packetStats = mapResultSetToPacketStats(rs);
                    if (packetStats != null) {
                        packetStatsList.add(packetStats);
                    }
                }
            }
        } catch (SQLException e) {
            logger.error("Error fetching packet stats for service: {}", serviceId, e);
        }

        return packetStatsList;
    }

    /**
     * 获取指定服务在指定时间段内的包统计数据
     * @param serviceId 服务ID
     * @param periodMinutes 时间段（分钟数）
     * @return 包统计数据列表
     */
    public List<PacketStats> getPacketStatsByServiceIdAndPeriod(String serviceId, int periodMinutes,boolean send) {
        List<PacketStats> packetStatsList = new ArrayList<>();

        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement stmt = conn.prepareStatement(send?SELECT_SEND_PACKET_STATS_BY_SERVICE_AND_PERIOD:SELECT_RECV_PACKET_STATS_BY_SERVICE_AND_PERIOD)) {
            
            stmt.setString(1, serviceId);
            stmt.setInt(2, periodMinutes);

            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    PacketStats packetStats = mapResultSetToPacketStats(rs);
                    if (packetStats != null) {
                        packetStatsList.add(packetStats);
                    }
                }
            }
        } catch (SQLException e) {
            logger.error("Error fetching packet stats for service: {} with period: {} minutes", serviceId, periodMinutes, e);
        }

        return packetStatsList;
    }

    /**
     * 将ResultSet映射为PacketStats对象
     * @param rs ResultSet对象
     * @return PacketStats对象
     * @throws SQLException SQL异常
     */
    private PacketStats mapResultSetToPacketStats(ResultSet rs) throws SQLException {
        int packType = rs.getInt("pack_type");
        long totalCount = rs.getLong("total_count");

        // 将数据库中的pack_type映射到PacketType枚举
        PacketType packetType = mapPackTypeToPacketType(packType);
        if (packetType == null) {
            logger.warn("Unknown pack_type: {}, skipping", packType);
            return null;
        }

        PacketStats packetStats = new PacketStats();
        packetStats.setPacketType(packetType);
        packetStats.setPacketCount(new Uint32((int) totalCount));

        return packetStats;
    }

    /**
     * 获取指定服务当天的发送包统计数据（基于count字段为1且begintime为当天的记录）
     * @param serviceId 服务ID
     * @return 包含总包数和总字节数的Map，键为"totalCount"和"totalBytes"
     */
    public Map<String, Long> getSendStatsByServiceId(String serviceId) {
        Map<String, Long> result = new HashMap<>();
        result.put("totalCount", 0L);
        result.put("totalBytes", 0L);

        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement stmt = conn.prepareStatement(SELECT_SEND_STATS_BY_SERVICE)) {

            stmt.setString(1, serviceId);

            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    result.put("totalCount", rs.getLong("total_count"));
                    result.put("totalBytes", rs.getLong("total_bytes"));
                }
            }
        } catch (SQLException e) {
            logger.error("Error fetching send stats for service: {}", serviceId, e);
        }

        return result;
    }

    /**
     * 获取指定服务当天的接收包统计数据（基于count_r字段为1且begintime为当天的记录）
     * @param serviceId 服务ID
     * @return 包含总包数和总字节数的Map，键为"totalCount"和"totalBytes"
     */
    public Map<String, Long> getRecvStatsByServiceId(String serviceId) {
        Map<String, Long> result = new HashMap<>();
        result.put("totalCount", 0L);
        result.put("totalBytes", 0L);

        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement stmt = conn.prepareStatement(SELECT_RECV_STATS_BY_SERVICE)) {

            stmt.setString(1, serviceId);

            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    result.put("totalCount", rs.getLong("total_count"));
                    result.put("totalBytes", rs.getLong("total_bytes"));
                }
            }
        } catch (SQLException e) {
            logger.error("Error fetching recv stats for service: {}", serviceId, e);
        }

        return result;
    }

    /**
     * 调试方法：查看指定服务的udp_audit表数据样本
     * @param serviceId 服务ID
     */
    public void debugUdpAuditData(String serviceId) {
        String debugSql = "SELECT serviceid, begintime, count, count_r, bytes FROM udp_audit WHERE serviceid = ? LIMIT 5";

        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement stmt = conn.prepareStatement(debugSql)) {

            stmt.setString(1, serviceId);

            try (ResultSet rs = stmt.executeQuery()) {
                logger.info("=== Debug UDP Audit Data for Service {} ===", serviceId);
                while (rs.next()) {
                    logger.info("ServiceId: {}, BeginTime: {}, Count: {}, Count_r: {}, Bytes: {}",
                            rs.getString("serviceid"),
                            rs.getTimestamp("begintime"),
                            rs.getInt("count"),
                            rs.getInt("count_r"),
                            rs.getLong("bytes"));
                }
                logger.info("=== End Debug Data ===");
            }
        } catch (SQLException e) {
            logger.error("Error debugging udp_audit data for service: {}", serviceId, e);
        }
    }

    /**
     * 将数据库中的pack_type值映射到PacketType枚举
     * @param packType 数据库中的pack_type值
     * @return PacketType枚举值
     */
    private PacketType mapPackTypeToPacketType(int packType) {
        switch (packType) {
            case 1:
                return PacketType.cim;
            case 2:
                return PacketType.slgs;
            case 3:
                return PacketType.lcs;
            case 4:
                return PacketType.bsm;
            case 9:
                return PacketType.other;
            default:
                return null;
        }
    }
}
