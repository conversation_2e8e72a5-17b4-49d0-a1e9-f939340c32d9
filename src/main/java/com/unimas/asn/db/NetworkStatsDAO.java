package com.unimas.asn.db;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

/**
 * Data Access Object for network_stats_daily table
 */
public class NetworkStatsDAO {
    private static final Logger logger = LoggerFactory.getLogger(NetworkStatsDAO.class);

    private static final String INSERT_OR_UPDATE_STATS =
            "INSERT INTO network_stats_daily (interface_name, stats_date, rx_packets, rx_bytes, tx_packets, tx_bytes) " +
            "VALUES (?, ?, ?, ?, ?, ?) " +
            "ON DUPLICATE KEY UPDATE " +
            "rx_packets = VALUES(rx_packets), " +
            "rx_bytes = VALUES(rx_bytes), " +
            "tx_packets = VALUES(tx_packets), " +
            "tx_bytes = VALUES(tx_bytes), " +
            "updated_time = CURRENT_TIMESTAMP";

    private static final String SELECT_STATS_BY_DATE =
            "SELECT interface_name, rx_packets, rx_bytes, tx_packets, tx_bytes " +
            "FROM network_stats_daily " +
            "WHERE stats_date = ?";

    private static final String SELECT_STATS_BY_INTERFACE_AND_DATE =
            "SELECT rx_packets, rx_bytes, tx_packets, tx_bytes " +
            "FROM network_stats_daily " +
            "WHERE interface_name = ? AND stats_date = ?";

    private static final String SELECT_STATS_BY_INTERFACE_AND_DATE_RANGE =
            "SELECT stats_date, rx_packets, rx_bytes, tx_packets, tx_bytes " +
            "FROM network_stats_daily " +
            "WHERE interface_name = ? AND stats_date BETWEEN ? AND ? " +
            "ORDER BY stats_date";

    /**
     * 网络接口每日统计数据类
     */
    public static class DailyNetworkStats {
        private final String interfaceName;
        private final LocalDate statsDate;
        private final long rxPackets;
        private final long rxBytes;
        private final long txPackets;
        private final long txBytes;

        public DailyNetworkStats(String interfaceName, LocalDate statsDate, 
                               long rxPackets, long rxBytes, long txPackets, long txBytes) {
            this.interfaceName = interfaceName;
            this.statsDate = statsDate;
            this.rxPackets = rxPackets;
            this.rxBytes = rxBytes;
            this.txPackets = txPackets;
            this.txBytes = txBytes;
        }

        public String getInterfaceName() { return interfaceName; }
        public LocalDate getStatsDate() { return statsDate; }
        public long getRxPackets() { return rxPackets; }
        public long getRxBytes() { return rxBytes; }
        public long getTxPackets() { return txPackets; }
        public long getTxBytes() { return txBytes; }

        @Override
        public String toString() {
            return "DailyNetworkStats{" +
                    "interfaceName='" + interfaceName + '\'' +
                    ", statsDate=" + statsDate +
                    ", rxPackets=" + rxPackets +
                    ", rxBytes=" + rxBytes +
                    ", txPackets=" + txPackets +
                    ", txBytes=" + txBytes +
                    '}';
        }
    }

    /**
     * 保存或更新网络接口的每日统计数据
     * @param interfaceName 接口名称
     * @param statsDate 统计日期
     * @param rxPackets 接收包数
     * @param rxBytes 接收字节数
     * @param txPackets 发送包数
     * @param txBytes 发送字节数
     * @return 是否成功
     */
    public boolean saveOrUpdateDailyStats(String interfaceName, LocalDate statsDate,
                                        long rxPackets, long rxBytes, long txPackets, long txBytes) {
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement stmt = conn.prepareStatement(INSERT_OR_UPDATE_STATS)) {
            
            stmt.setString(1, interfaceName);
            stmt.setDate(2, java.sql.Date.valueOf(statsDate));
            stmt.setLong(3, rxPackets);
            stmt.setLong(4, rxBytes);
            stmt.setLong(5, txPackets);
            stmt.setLong(6, txBytes);
            
            int rowsAffected = stmt.executeUpdate();
            logger.debug("Saved daily stats for interface {} on {}: {} rows affected", 
                        interfaceName, statsDate, rowsAffected);
            return rowsAffected > 0;
            
        } catch (SQLException e) {
            logger.error("Error saving daily stats for interface {} on {}", interfaceName, statsDate, e);
            return false;
        }
    }

    /**
     * 获取指定日期的所有接口统计数据
     * @param statsDate 统计日期
     * @return 接口名称到统计数据的映射
     */
    public Map<String, DailyNetworkStats> getDailyStatsByDate(LocalDate statsDate) {
        Map<String, DailyNetworkStats> statsMap = new HashMap<>();
        
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement stmt = conn.prepareStatement(SELECT_STATS_BY_DATE)) {
            
            stmt.setDate(1, java.sql.Date.valueOf(statsDate));
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    String interfaceName = rs.getString("interface_name");
                    long rxPackets = rs.getLong("rx_packets");
                    long rxBytes = rs.getLong("rx_bytes");
                    long txPackets = rs.getLong("tx_packets");
                    long txBytes = rs.getLong("tx_bytes");
                    
                    DailyNetworkStats stats = new DailyNetworkStats(
                            interfaceName, statsDate, rxPackets, rxBytes, txPackets, txBytes);
                    statsMap.put(interfaceName, stats);
                }
            }
            
        } catch (SQLException e) {
            logger.error("Error fetching daily stats for date {}", statsDate, e);
        }
        
        return statsMap;
    }

    /**
     * 获取指定接口和日期的统计数据
     * @param interfaceName 接口名称
     * @param statsDate 统计日期
     * @return 统计数据，如果不存在则返回null
     */
    public DailyNetworkStats getDailyStatsByInterfaceAndDate(String interfaceName, LocalDate statsDate) {
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement stmt = conn.prepareStatement(SELECT_STATS_BY_INTERFACE_AND_DATE)) {
            
            stmt.setString(1, interfaceName);
            stmt.setDate(2, java.sql.Date.valueOf(statsDate));
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    long rxPackets = rs.getLong("rx_packets");
                    long rxBytes = rs.getLong("rx_bytes");
                    long txPackets = rs.getLong("tx_packets");
                    long txBytes = rs.getLong("tx_bytes");
                    
                    return new DailyNetworkStats(interfaceName, statsDate, 
                                               rxPackets, rxBytes, txPackets, txBytes);
                }
            }
            
        } catch (SQLException e) {
            logger.error("Error fetching daily stats for interface {} on {}", interfaceName, statsDate, e);
        }
        
        return null;
    }
}
