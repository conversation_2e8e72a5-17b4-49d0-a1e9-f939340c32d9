package com.unimas.asn.service;

import com.unimas.asn.servicemanager.servicemanagementhttp.Network;
import com.unimas.asn.util.DeviceConfigReader;
import com.unimas.asn.util.NetworkStatsReader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 每日网络统计服务
 * 负责管理网络统计的基线值，计算每日增量统计
 */
public class DailyNetworkStatsService {
    private static final Logger logger = LoggerFactory.getLogger(DailyNetworkStatsService.class);
    
    // 基线数据存储文件路径
    private static final String BASELINE_FILE_PATH = "/etc/unimas/tomcat/conf/network_baseline.properties";
    private static final String BACKUP_BASELINE_FILE_PATH = "/tmp/network_baseline.properties";
    
    private final ScheduledExecutorService scheduler;
    private volatile Map<String, Long> todayBaseline = new HashMap<>();
    private volatile LocalDate currentDate;
    
    public DailyNetworkStatsService() {
        this.scheduler = Executors.newScheduledThreadPool(1);
        this.currentDate = LocalDate.now();
        loadTodayBaseline();
    }
    
    /**
     * 启动服务
     */
    public void start() {
        logger.info("Starting Daily Network Stats Service");
        
        // 立即检查是否需要重置基线
        checkAndResetBaseline();
        
        // 每小时检查一次是否需要重置基线（防止服务在午夜时未运行）
        scheduler.scheduleAtFixedRate(this::checkAndResetBaseline, 1, 60, TimeUnit.MINUTES);
        
        // 每10分钟保存一次当前统计数据作为备份
        scheduler.scheduleAtFixedRate(this::saveCurrentStatsAsBackup, 10, 10, TimeUnit.MINUTES);
        
        logger.info("Daily Network Stats Service started successfully");
    }
    
    /**
     * 停止服务
     */
    public void stop() {
        logger.info("Stopping Daily Network Stats Service");
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
        logger.info("Daily Network Stats Service stopped");
    }
    
    /**
     * 获取当天的网络统计数据（增量值）
     * @param role 设备角色
     * @return 当天的网络统计数据
     */
    public Map<String, Long> getTodayNetworkStats(Network role) {
        // 获取当前累计统计
        Map<String, Long> currentStats = NetworkStatsReader.getNetworkStats(role);
        
        // 计算当天增量
        Map<String, Long> todayStats = new HashMap<>();
        
        for (Map.Entry<String, Long> entry : currentStats.entrySet()) {
            String key = entry.getKey();
            long currentValue = entry.getValue();
            long baselineValue = todayBaseline.getOrDefault(key, 0L);
            
            // 计算增量，处理可能的数值回退（系统重启等）
            long todayValue = calculateIncrement(currentValue, baselineValue, key);
            todayStats.put(key, todayValue);
        }
        
        logger.debug("Today's network stats: {}", todayStats);
        return todayStats;
    }
    
    /**
     * 计算增量值，处理数值回退情况
     */
    private long calculateIncrement(long currentValue, long baselineValue, String statType) {
        if (currentValue >= baselineValue) {
            // 正常情况：当前值大于等于基线值
            return currentValue - baselineValue;
        } else {
            // 异常情况：当前值小于基线值（可能是系统重启）
            logger.warn("Detected value rollback for {}: current={}, baseline={}. Using current value as today's increment.", 
                       statType, currentValue, baselineValue);
            
            // 更新基线为0，当前值作为今天的增量
            todayBaseline.put(statType, 0L);
            return currentValue;
        }
    }
    
    /**
     * 检查并重置基线（每天午夜执行）
     */
    private void checkAndResetBaseline() {
        LocalDate today = LocalDate.now();
        
        if (!today.equals(currentDate)) {
            logger.info("Date changed from {} to {}, resetting network stats baseline", currentDate, today);
            resetDailyBaseline();
            currentDate = today;
        }
    }
    
    /**
     * 重置每日基线
     */
    private void resetDailyBaseline() {
        try {
            // 获取设备角色
            DeviceConfigReader deviceConfig = DeviceConfigReader.getInstance();
            Network role = deviceConfig.getNetwork();
            
            // 获取当前网络统计作为新的基线
            Map<String, Long> currentStats = NetworkStatsReader.getNetworkStats(role);
            
            // 更新基线
            todayBaseline = new HashMap<>(currentStats);
            
            // 保存基线到文件
            saveBaselineToFile();
            
            logger.info("Daily baseline reset successfully for date: {}", currentDate);
            logger.debug("New baseline: {}", todayBaseline);
            
        } catch (Exception e) {
            logger.error("Error resetting daily baseline", e);
        }
    }
    
    /**
     * 加载今天的基线数据
     */
    private void loadTodayBaseline() {
        Properties props = new Properties();
        File baselineFile = new File(BASELINE_FILE_PATH);
        
        try {
            if (baselineFile.exists()) {
                try (FileInputStream fis = new FileInputStream(baselineFile)) {
                    props.load(fis);
                    
                    // 检查基线日期是否是今天
                    String savedDate = props.getProperty("baseline.date");
                    if (currentDate.toString().equals(savedDate)) {
                        // 加载基线数据
                        todayBaseline.put("sendPkgNum", Long.parseLong(props.getProperty("sendPkgNum", "0")));
                        todayBaseline.put("sendPkgSize", Long.parseLong(props.getProperty("sendPkgSize", "0")));
                        todayBaseline.put("recvPkgNum", Long.parseLong(props.getProperty("recvPkgNum", "0")));
                        todayBaseline.put("recvPkgSize", Long.parseLong(props.getProperty("recvPkgSize", "0")));
                        
                        logger.info("Loaded today's baseline from file: {}", todayBaseline);
                        return;
                    } else {
                        logger.info("Baseline file date ({}) doesn't match today ({}), will reset baseline", 
                                   savedDate, currentDate);
                    }
                }
            }
        } catch (Exception e) {
            logger.warn("Error loading baseline file, will reset baseline", e);
        }
        
        // 如果无法加载有效基线，则重置
        resetDailyBaseline();
    }
    
    /**
     * 保存基线数据到文件
     */
    private void saveBaselineToFile() {
        Properties props = new Properties();
        props.setProperty("baseline.date", currentDate.toString());
        props.setProperty("baseline.timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        
        for (Map.Entry<String, Long> entry : todayBaseline.entrySet()) {
            props.setProperty(entry.getKey(), entry.getValue().toString());
        }
        
        // 确保目录存在
        File baselineFile = new File(BASELINE_FILE_PATH);
        baselineFile.getParentFile().mkdirs();
        
        try (FileOutputStream fos = new FileOutputStream(baselineFile)) {
            props.store(fos, "Network Statistics Baseline - Generated at " + LocalDateTime.now());
            logger.debug("Baseline saved to file: {}", BASELINE_FILE_PATH);
        } catch (IOException e) {
            logger.error("Error saving baseline to file: {}", BASELINE_FILE_PATH, e);
            
            // 尝试保存到备份位置
            try (FileOutputStream fos = new FileOutputStream(BACKUP_BASELINE_FILE_PATH)) {
                props.store(fos, "Network Statistics Baseline Backup - Generated at " + LocalDateTime.now());
                logger.info("Baseline saved to backup location: {}", BACKUP_BASELINE_FILE_PATH);
            } catch (IOException e2) {
                logger.error("Error saving baseline to backup location", e2);
            }
        }
    }
    
    /**
     * 保存当前统计数据作为备份
     */
    private void saveCurrentStatsAsBackup() {
        try {
            DeviceConfigReader deviceConfig = DeviceConfigReader.getInstance();
            Network role = deviceConfig.getNetwork();
            Map<String, Long> currentStats = NetworkStatsReader.getNetworkStats(role);
            
            Properties props = new Properties();
            props.setProperty("backup.timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            props.setProperty("backup.date", LocalDate.now().toString());
            
            for (Map.Entry<String, Long> entry : currentStats.entrySet()) {
                props.setProperty("current." + entry.getKey(), entry.getValue().toString());
            }
            
            File backupFile = new File("/tmp/network_current_backup.properties");
            try (FileOutputStream fos = new FileOutputStream(backupFile)) {
                props.store(fos, "Current Network Statistics Backup");
            }
            
        } catch (Exception e) {
            logger.debug("Error saving current stats backup", e);
        }
    }
}
