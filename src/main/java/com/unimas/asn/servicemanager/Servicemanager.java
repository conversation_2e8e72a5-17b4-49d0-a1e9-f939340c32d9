/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: fb (Trial), License 88957Z 88957Z. */
/* Abstract syntax: service */
/* ASN.1 Java project: com.unimas.asn.servicemanager.Servicemanager */
/* Created: <PERSON>e Jun 10 14:20:32 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.unimas.asn.servicemanager -coer -root -sampleCode pdus
 * -messageFormat msvc D:/history/ASN/service.asn
 */


package com.unimas.asn.servicemanager;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
  A class for the Servicemanager ASN.1/Java  project.
*/
public class Servicemanager extends ASN1Project {

    /**
     * Initialize the pdu decoder.
     */
    private static final ProjectInfo c_projectInfo = new ProjectInfo (
	null,
	new byte[] {
	    (byte)0x0b, (byte)0xc4, (byte)0xb4, (byte)0xf9, (byte)0x6d,
	    (byte)0xa5, (byte)0x3f, (byte)0x2c, (byte)0xf6, (byte)0xa5,
	    (byte)0x55, (byte)0x67, (byte)0x9e, (byte)0xa6, (byte)0x8c,
	    (byte)0x92, (byte)0xc1, (byte)0x58, (byte)0x2b, (byte)0x68,
	    (byte)0xc7, (byte)0xc3, (byte)0x09, (byte)0x9d, (byte)0xf7,
	    (byte)0x25, (byte)0x7a, (byte)0xb6, (byte)0x5a, (byte)0x4e,
	    (byte)0xa8, (byte)0xcd, (byte)0x30, (byte)0x20, (byte)0x46,
	    (byte)0x09, (byte)0x4c, (byte)0xf4, (byte)0x3b, (byte)0x36,
	    (byte)0xe3, (byte)0x05, (byte)0x3b, (byte)0x07, (byte)0x9a,
	    (byte)0xa5, (byte)0x8a, (byte)0xff, (byte)0xbb, (byte)0x17,
	    (byte)0x7b, (byte)0xa9, (byte)0x2e, (byte)0x17, (byte)0xf9,
	    (byte)0x5a, (byte)0xdc, (byte)0x31, (byte)0x4d, (byte)0xf7,
	    (byte)0x9d, (byte)0x09, (byte)0xc3, (byte)0xcf, (byte)0x09,
	    (byte)0x8e, (byte)0x8d, (byte)0x64, (byte)0x19, (byte)0x29,
	    (byte)0x3d, (byte)0x76, (byte)0x91, (byte)0x76, (byte)0x74,
	    (byte)0x12, (byte)0xe8, (byte)0xa4, (byte)0xc4
	},
	"2025/06/12"
    );
    
    /**
     * Get the project descriptor of 'this' object.
     */
    public ProjectInfo getProjectInfo()
    {
	return c_projectInfo;
    }
    
    private static final ASN1Project c_project = new Servicemanager();

    /**
     * Methods for accessing Coders.
     */
    public static Coder getDefaultCoder()
    {
	return createCOERCoder(c_project);
    }
    
    public static OERCoder getOERCoder()
    {
	return createOERCoder(c_project);
    }
    
    public static COERCoder getCOERCoder()
    {
	return createCOERCoder(c_project);
    }
    
}
