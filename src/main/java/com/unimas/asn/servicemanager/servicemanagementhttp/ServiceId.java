/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: fb (Trial), License 88957Z 88957Z. */
/* Abstract syntax: service */
/* ASN.1 Java project: com.unimas.asn.servicemanager.Servicemanager */
/* Created: <PERSON>e Jun 10 14:20:32 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.unimas.asn.servicemanager -coer -root -sampleCode pdus
 * -messageFormat msvc D:/history/ASN/service.asn
 */


package com.unimas.asn.servicemanager.servicemanagementhttp;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the ServiceId ASN.1 type included in the ServiceManagementHTTP ASN.1 module.
 * @see INTEGER
 */

public class ServiceId extends INTEGER {
    
    /**
     * The default constructor.
     */
    public ServiceId()
    {
    }
    
    public ServiceId(short value)
    {
	super(value);
    }
    
    public ServiceId(int value)
    {
	super(value);
    }
    
    public ServiceId(long value)
    {
	super(value);
    }
    
    /**
     * Initialize the type descriptor.
     */
    private static final IntegerInfo c_typeinfo = new IntegerInfo (
	new Tags (
	    new short[] {
		0x0002
	    }
	),
	new QName (
	    "com.unimas.asn.servicemanager.servicemanagementhttp",
	    "ServiceId"
	),
	new QName (
	    "ServiceManagementHTTP",
	    "ServiceId"
	),
	536603,
	new ValueRangeConstraint (
	    new AbstractBounds(
		new ServiceId(0), 
		new ServiceId(32767),
		0
	    )
	),
	new Bounds (
	    java.lang.Long.valueOf(0),
	    java.lang.Long.valueOf(32767)
	),
	null,
	2
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' ServiceId object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' ServiceId object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
} // End class definition for ServiceId
