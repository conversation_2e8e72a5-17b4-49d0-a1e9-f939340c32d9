/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: fb (Trial), License 88957Z 88957Z. */
/* Abstract syntax: service */
/* ASN.1 Java project: com.unimas.asn.servicemanager.Servicemanager */
/* Created: <PERSON><PERSON> Jun 10 14:20:32 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.unimas.asn.servicemanager -coer -root -sampleCode pdus
 * -messageFormat msvc D:/history/ASN/service.asn
 */


package com.unimas.asn.servicemanager.servicemanagementhttp;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the String ASN.1 type included in the ServiceManagementHTTP ASN.1 module.
 * @see UTF8String16
 */

public class String_ extends UTF8String16 {
    
    /**
     * The default constructor.
     */
    public String_()
    {
    }
    
    /**
     * Construct from a String type.
     * @param value the String object to set this object to.
     */
    public String_(String value)
    {
	super(value);
    }
    
    
    /**
     * Construct from a char[] type.
     * @param value the char[] object to set this object to.
     */
    public String_(char[] value)
    {
	super(value);
    }
    
    
    /**
     * Construct from a int[] type.
     * @param value the int[] object to set this object to.
     */
    public String_(int[] value)
    {
	super (value);
    }
    
    
    /**
     * Construct from a byte[] type.
     * @param value the byte[] object to set this object to.
     */
    public String_(byte[] value)
    {
	super (value);
    }
    
    /**
     * Initialize the type descriptor.
     */
    private static final VectorInfo c_typeinfo = new VectorInfo (
	new Tags (
	    new short[] {
		0x000c
	    }
	),
	new QName (
	    "com.unimas.asn.servicemanager.servicemanagementhttp",
	    "String_"
	),
	new QName (
	    "ServiceManagementHTTP",
	    "String"
	),
	536603,
	null,
	null
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' String_ object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' String_ object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * The type is a PDU.
     */
    public boolean isPDU()
    {
	return true;
    }
    
} // End class definition for String_
