/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: fb (Trial), License 88957Z 88957Z. */
/* Abstract syntax: service */
/* ASN.1 Java project: com.unimas.asn.servicemanager.Servicemanager */
/* Created: <PERSON><PERSON> Jun 10 14:20:32 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.unimas.asn.servicemanager -coer -root -sampleCode pdus
 * -messageFormat msvc D:/history/ASN/service.asn
 */


package com.unimas.asn.servicemanager.servicemanagementhttp;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the Uint32 ASN.1 type included in the ServiceManagementHTTP ASN.1 module.
 * @see INTEGER
 */

public class Uint32 extends INTEGER {
    
    /**
     * The default constructor.
     */
    public Uint32()
    {
    }
    
    public Uint32(short value)
    {
	super(value);
    }
    
    public Uint32(int value)
    {
	super(value);
    }
    
    public Uint32(long value)
    {
	super(value);
    }
    
    /**
     * Initialize the type descriptor.
     */
    private static final IntegerInfo c_typeinfo = new IntegerInfo (
	new Tags (
	    new short[] {
		0x0002
	    }
	),
	new QName (
	    "com.unimas.asn.servicemanager.servicemanagementhttp",
	    "Uint32"
	),
	new QName (
	    "ServiceManagementHTTP",
	    "Uint32"
	),
	536603,
	new ValueRangeConstraint (
	    new AbstractBounds(
		new Uint32(0), 
		new Uint32(4294967295L),
		0
	    )
	),
	new Bounds (
	    java.lang.Long.valueOf(0),
	    java.lang.Long.valueOf(4294967295L)
	),
	null,
	4
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' Uint32 object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' Uint32 object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
} // End class definition for Uint32
