/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: fb (Trial), License 88957Z 88957Z. */
/* Abstract syntax: service */
/* ASN.1 Java project: com.unimas.asn.servicemanager.Servicemanager */
/* Created: Tue Jun 10 14:20:32 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.unimas.asn.servicemanager -coer -root -sampleCode pdus
 * -messageFormat msvc D:/history/ASN/service.asn
 */


package com.unimas.asn.servicemanager.servicemanagementhttp;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the QueryHostMngResponse ASN.1 type included in the ServiceManagementHTTP ASN.1 module.
 * @see Sequence
 */

public class QueryHostMngResponse extends Sequence {
    
    /**
     * The default constructor.
     */
    public QueryHostMngResponse()
    {
    }
    
    /**
     * Construct with AbstractData components.
     */
    public QueryHostMngResponse(ContentMessageType messageType, 
		    IPAddress centerIP, PortNumber authPort, 
		    PortNumber alarmPort, PortNumber certMngPort, 
		    PortNumber sgPort)
    {
	setMessageType(messageType);
	setCenterIP(centerIP);
	setAuthPort(authPort);
	setAlarmPort(alarmPort);
	setCertMngPort(certMngPort);
	setSgPort(sgPort);
    }
    
    public void initComponents()
    {
	mComponents[0] = ContentMessageType.setInterfaceIpService;
	mComponents[1] = new IPAddress();
	mComponents[2] = new PortNumber();
	mComponents[3] = new PortNumber();
	mComponents[4] = new PortNumber();
	mComponents[5] = new PortNumber();
    }
    
    // Instance initializer
    {
	mComponents = new AbstractData[6];
    }
    
    // Method to create a specific component instance
    public AbstractData createInstance(int index)
    {
	switch (index) {
	    case 0:
		return ContentMessageType.setInterfaceIpService;
	    case 1:
		return new IPAddress();
	    case 2:
		return new PortNumber();
	    case 3:
		return new PortNumber();
	    case 4:
		return new PortNumber();
	    case 5:
		return new PortNumber();
	    default:
		throw new InternalError("AbstractCollection.createInstance()");
	}
	
    }
    
    
    // Methods for field "messageType"
    public ContentMessageType getMessageType()
    {
	return (ContentMessageType)mComponents[0];
    }
    
    public void setMessageType(ContentMessageType messageType)
    {
	mComponents[0] = messageType;
    }
    
    
    // Methods for field "centerIP"
    public IPAddress getCenterIP()
    {
	return (IPAddress)mComponents[1];
    }
    
    public void setCenterIP(IPAddress centerIP)
    {
	mComponents[1] = centerIP;
    }
    
    
    // Methods for field "authPort"
    public PortNumber getAuthPort()
    {
	return (PortNumber)mComponents[2];
    }
    
    public void setAuthPort(PortNumber authPort)
    {
	mComponents[2] = authPort;
    }
    
    
    // Methods for field "alarmPort"
    public PortNumber getAlarmPort()
    {
	return (PortNumber)mComponents[3];
    }
    
    public void setAlarmPort(PortNumber alarmPort)
    {
	mComponents[3] = alarmPort;
    }
    
    
    // Methods for field "certMngPort"
    public PortNumber getCertMngPort()
    {
	return (PortNumber)mComponents[4];
    }
    
    public void setCertMngPort(PortNumber certMngPort)
    {
	mComponents[4] = certMngPort;
    }
    
    
    // Methods for field "sgPort"
    public PortNumber getSgPort()
    {
	return (PortNumber)mComponents[5];
    }
    
    public void setSgPort(PortNumber sgPort)
    {
	mComponents[5] = sgPort;
    }
    
    
    /**
     * Initialize the type descriptor.
     */
    private static final SequenceInfo c_typeinfo = new SequenceInfo (
	new Tags (
	    new short[] {
		0x0010
	    }
	),
	new QName (
	    "com.unimas.asn.servicemanager.servicemanagementhttp",
	    "QueryHostMngResponse"
	),
	new QName (
	    "ServiceManagementHTTP",
	    "QueryHostMngResponse"
	),
	536603,
	null,
	new FieldsList (
	    new SequenceFieldInfo[] {
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new EnumeratedInfo (
			    new Tags (
				new short[] {
				    (short)0x8000
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"ContentMessageType"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"ContentMessageType"
			    ),
			    536607,
			    null,
			    new MemberList (
				new MemberListElement[] {
				    new MemberListElement (
					"setInterfaceIpService",
					0
				    ),
				    new MemberListElement (
					"addService",
					1
				    ),
				    new MemberListElement (
					"updateService",
					2
				    ),
				    new MemberListElement (
					"deleteService",
					3
				    ),
				    new MemberListElement (
					"controlService",
					4
				    ),
				    new MemberListElement (
					"queryServiceStatus",
					5
				    ),
				    new MemberListElement (
					"queryServiceConfig",
					6
				    ),
				    new MemberListElement (
					"reportAlarm",
					7
				    ),
				    new MemberListElement (
					"queryWorkStatus",
					8
				    ),
				    new MemberListElement (
					"getAllServiceIds",
					9
				    ),
				    new MemberListElement (
					"sendPacketStats",
					10
				    ),
				    new MemberListElement (
					"receivePacketStats",
					11
				    ),
				    new MemberListElement (
					"checkCommStatusService",
					12
				    ),
				    new MemberListElement (
					"hostMngService",
					13
				    ),
				    new MemberListElement (
					"sourceDeviceService",
					14
				    ),
				    new MemberListElement (
					"iprouteService",
					15
				    )
				}
			    ),
			    0,
			    ContentMessageType.setInterfaceIpService
			)
		    ),
		    "messageType",
		    0,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new ChoiceInfo (
			    new Tags (
				new short[] {
				    (short)0x8001
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"IPAddress"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"IPAddress"
			    ),
			    536603,
			    null,
			    new FieldsRef (
				new QName (
				    "com.unimas.asn.servicemanager.servicemanagementhttp",
				    "IPAddress"
				)
			    ),
			    0,
			    new TagDecoder (
				new TagDecoderElement[] {
				    new TagDecoderElement((short)0x8000, 0),
				    new TagDecoderElement((short)0x8001, 1)
				}
			    )
			)
		    ),
		    "centerIP",
		    1,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new IntegerInfo (
			    new Tags (
				new short[] {
				    (short)0x8002
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"PortNumber"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"PortNumber"
			    ),
			    536603,
			    new ValueRangeConstraint (
				new AbstractBounds(
				    new PortNumber(1025), 
				    new PortNumber(65535),
				    0
				)
			    ),
			    new Bounds (
				java.lang.Long.valueOf(1025),
				java.lang.Long.valueOf(65535)
			    ),
			    null,
			    2
			)
		    ),
		    "authPort",
		    2,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new IntegerInfo (
			    new Tags (
				new short[] {
				    (short)0x8003
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"PortNumber"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"PortNumber"
			    ),
			    536603,
			    new ValueRangeConstraint (
				new AbstractBounds(
				    new PortNumber(1025), 
				    new PortNumber(65535),
				    0
				)
			    ),
			    new Bounds (
				java.lang.Long.valueOf(1025),
				java.lang.Long.valueOf(65535)
			    ),
			    null,
			    2
			)
		    ),
		    "alarmPort",
		    3,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new IntegerInfo (
			    new Tags (
				new short[] {
				    (short)0x8004
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"PortNumber"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"PortNumber"
			    ),
			    536603,
			    new ValueRangeConstraint (
				new AbstractBounds(
				    new PortNumber(1025), 
				    new PortNumber(65535),
				    0
				)
			    ),
			    new Bounds (
				java.lang.Long.valueOf(1025),
				java.lang.Long.valueOf(65535)
			    ),
			    null,
			    2
			)
		    ),
		    "certMngPort",
		    4,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new IntegerInfo (
			    new Tags (
				new short[] {
				    (short)0x8005
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"PortNumber"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"PortNumber"
			    ),
			    536603,
			    new ValueRangeConstraint (
				new AbstractBounds(
				    new PortNumber(1025), 
				    new PortNumber(65535),
				    0
				)
			    ),
			    new Bounds (
				java.lang.Long.valueOf(1025),
				java.lang.Long.valueOf(65535)
			    ),
			    null,
			    2
			)
		    ),
		    "sgPort",
		    5,
		    2,
		    null
		)
	    }
	),
	0,
	new TagDecoders (
	    new TagDecoder[] {
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8000, 0)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8001, 1)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8002, 2)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8003, 3)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8004, 4)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8005, 5)
		    }
		)
	    }
	),
	0
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' QueryHostMngResponse object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' QueryHostMngResponse object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
} // End class definition for QueryHostMngResponse
