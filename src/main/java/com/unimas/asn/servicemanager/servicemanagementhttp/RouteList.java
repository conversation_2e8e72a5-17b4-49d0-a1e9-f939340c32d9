/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: fb (Trial), License 88957Z 88957Z. */
/* Abstract syntax: service */
/* ASN.1 Java project: com.unimas.asn.servicemanager.Servicemanager */
/* Created: <PERSON>e Jun 10 14:20:32 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.unimas.asn.servicemanager -coer -root -sampleCode pdus
 * -messageFormat msvc D:/history/ASN/service.asn
 */


package com.unimas.asn.servicemanager.servicemanagementhttp;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the RouteList ASN.1 type included in the ServiceManagementHTTP ASN.1 module.
 * @see Sequence
 */

public class RouteList extends Sequence {
    
    /**
     * The default constructor.
     */
    public RouteList()
    {
    }
    
    /**
     * Construct with AbstractData components.
     */
    public RouteList(IPAddress destination, IPAddress subnetMask, 
		    IPAddress gateway, INTEGER metric)
    {
	setDestination(destination);
	setSubnetMask(subnetMask);
	setGateway(gateway);
	setMetric(metric);
    }
    
    /**
     * Construct with components.
     */
    public RouteList(IPAddress destination, IPAddress subnetMask, 
		    IPAddress gateway, long metric)
    {
	this(destination, subnetMask, gateway, new INTEGER(metric));
    }
    
    public void initComponents()
    {
	mComponents[0] = new IPAddress();
	mComponents[1] = new IPAddress();
	mComponents[2] = new IPAddress();
	mComponents[3] = new INTEGER();
    }
    
    // Instance initializer
    {
	mComponents = new AbstractData[4];
    }
    
    // Method to create a specific component instance
    public AbstractData createInstance(int index)
    {
	switch (index) {
	    case 0:
		return new IPAddress();
	    case 1:
		return new IPAddress();
	    case 2:
		return new IPAddress();
	    case 3:
		return new INTEGER();
	    default:
		throw new InternalError("AbstractCollection.createInstance()");
	}
	
    }
    
    
    // Methods for field "destination"
    public IPAddress getDestination()
    {
	return (IPAddress)mComponents[0];
    }
    
    public void setDestination(IPAddress destination)
    {
	mComponents[0] = destination;
    }
    
    
    // Methods for field "subnetMask"
    public IPAddress getSubnetMask()
    {
	return (IPAddress)mComponents[1];
    }
    
    public void setSubnetMask(IPAddress subnetMask)
    {
	mComponents[1] = subnetMask;
    }
    
    
    // Methods for field "gateway"
    public IPAddress getGateway()
    {
	return (IPAddress)mComponents[2];
    }
    
    public void setGateway(IPAddress gateway)
    {
	mComponents[2] = gateway;
    }
    
    
    // Methods for field "metric"
    public long getMetric()
    {
	return ((INTEGER)mComponents[3]).longValue();
    }
    
    public void setMetric(long metric)
    {
	setMetric(new INTEGER(metric));
    }
    
    public void setMetric(INTEGER metric)
    {
	mComponents[3] = metric;
    }
    
    
    /**
     * Initialize the type descriptor.
     */
    private static final SequenceInfo c_typeinfo = new SequenceInfo (
	new Tags (
	    new short[] {
		0x0010
	    }
	),
	new QName (
	    "com.unimas.asn.servicemanager.servicemanagementhttp",
	    "RouteList"
	),
	new QName (
	    "ServiceManagementHTTP",
	    "RouteList"
	),
	536603,
	null,
	new FieldsList (
	    new SequenceFieldInfo[] {
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new ChoiceInfo (
			    new Tags (
				new short[] {
				    (short)0x8000
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"IPAddress"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"IPAddress"
			    ),
			    536603,
			    null,
			    new FieldsRef (
				new QName (
				    "com.unimas.asn.servicemanager.servicemanagementhttp",
				    "IPAddress"
				)
			    ),
			    0,
			    new TagDecoder (
				new TagDecoderElement[] {
				    new TagDecoderElement((short)0x8000, 0),
				    new TagDecoderElement((short)0x8001, 1)
				}
			    )
			)
		    ),
		    "destination",
		    0,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new ChoiceInfo (
			    new Tags (
				new short[] {
				    (short)0x8001
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"IPAddress"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"IPAddress"
			    ),
			    536603,
			    null,
			    new FieldsRef (
				new QName (
				    "com.unimas.asn.servicemanager.servicemanagementhttp",
				    "IPAddress"
				)
			    ),
			    0,
			    new TagDecoder (
				new TagDecoderElement[] {
				    new TagDecoderElement((short)0x8000, 0),
				    new TagDecoderElement((short)0x8001, 1)
				}
			    )
			)
		    ),
		    "subnetMask",
		    1,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new ChoiceInfo (
			    new Tags (
				new short[] {
				    (short)0x8002
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"IPAddress"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"IPAddress"
			    ),
			    536603,
			    null,
			    new FieldsRef (
				new QName (
				    "com.unimas.asn.servicemanager.servicemanagementhttp",
				    "IPAddress"
				)
			    ),
			    0,
			    new TagDecoder (
				new TagDecoderElement[] {
				    new TagDecoderElement((short)0x8000, 0),
				    new TagDecoderElement((short)0x8001, 1)
				}
			    )
			)
		    ),
		    "gateway",
		    2,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new IntegerInfo (
			    new Tags (
				new short[] {
				    (short)0x8003
				}
			    ),
			    new QName (
				"com.oss.asn1",
				"INTEGER"
			    ),
			    new QName (
				"builtin",
				"INTEGER"
			    ),
			    536603,
			    null,
			    null,
			    null,
			    0
			)
		    ),
		    "metric",
		    3,
		    2,
		    null
		)
	    }
	),
	0,
	new TagDecoders (
	    new TagDecoder[] {
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8000, 0)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8001, 1)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8002, 2)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8003, 3)
		    }
		)
	    }
	),
	0
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' com.unimas.asn.servicemanager.servicemanagementhttp.RouteList object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' com.unimas.asn.servicemanager.servicemanagementhttp.RouteList object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
} // End class definition for RouteList
