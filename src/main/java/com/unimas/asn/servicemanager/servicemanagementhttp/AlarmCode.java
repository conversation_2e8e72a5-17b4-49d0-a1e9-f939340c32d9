/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: fb (Trial), License 88957Z 88957Z. */
/* Abstract syntax: service */
/* ASN.1 Java project: com.unimas.asn.servicemanager.Servicemanager */
/* Created: <PERSON>e Jun 10 14:20:32 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.unimas.asn.servicemanager -coer -root -sampleCode pdus
 * -messageFormat msvc D:/history/ASN/service.asn
 */


package com.unimas.asn.servicemanager.servicemanagementhttp;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the AlarmCode ASN.1 type included in the ServiceManagementHTTP ASN.1 module.
 * @see Enumerated
 */

public final class AlarmCode extends Enumerated {
    
    /**
     * The default constructor.
     */
    private AlarmCode()
    {
	super(cFirstNumber);
    }
    
    protected AlarmCode(long value)
    {
	super(value);
    }
    
    /**
      An inner class that contains numeric values for ASN.1 ENUMERATED type.
      The values can be used in switch/case statements.
    */
    public static final class Value {
	public static final long illegalCertificate = 1;
	public static final long deviceException = 2;
	public static final long channelException = 3;
	public static final long protocolVerifyFailure = 4;
	public static final long keywordCheckFailure = 5;
	
    }
    // Named list definitions.
    private final static AlarmCode cNamedNumbers[] = {
	new AlarmCode(), 
	new AlarmCode(2), 
	new AlarmCode(3), 
	new AlarmCode(4), 
	new AlarmCode(5)
    };
    public static final AlarmCode illegalCertificate = cNamedNumbers[0];
    public static final AlarmCode deviceException = cNamedNumbers[1];
    public static final AlarmCode channelException = cNamedNumbers[2];
    public static final AlarmCode protocolVerifyFailure = cNamedNumbers[3];
    public static final AlarmCode keywordCheckFailure = cNamedNumbers[4];
    
    protected final static long cFirstNumber = 1;
    protected final static boolean cLinearNumbers = false;
    
    public Enumerated[] getNamedNumbers()
    {
	return cNamedNumbers;
    }
    
    public boolean hasLinearNumbers()
    {
	return cLinearNumbers;
    }
    
    public long getFirstNumber()
    {
	return cFirstNumber;
    }
    
    /**
     * This method is reserved for internal use and must not be invoked from the application code.
     * @exclude
     */
    public static int indexOfValue(long value)
    {
	if (value >= 1 && value <= 5)
	    return (int)(value - 1);
	else
	    return -1;
    }
    
    /**
     * Returns an enumerator with a specified value or null if the value
     * is not associated with any enumerators.
     *  @param value the value of the enumerator to return.
     *  @return an enumerator with a specified value.
     */
    
    public static AlarmCode valueOf(long value)
    {
	int inx = indexOfValue(value);
	
	if (inx < 0)
	    return null;
	else
	    return cNamedNumbers[inx];
    }
    
    /**
     * This method is reserved for internal use and must not be invoked from the application code.
     * @exclude
     */
    public int indexOf()
    {
	if (isUnknownEnumerator())
	    return -1;
	return indexOfValue(mValue);
    }
    
    /**
     * This method is reserved for internal use and must not be invoked from the application code.
     * @exclude
     */
    public Enumerated lookupValue(long value)
    {
	return valueOf(value);
    }
    
    /**
     * Initialize the type descriptor.
     */
    private static final EnumeratedInfo c_typeinfo = new EnumeratedInfo (
	new Tags (
	    new short[] {
		0x000a
	    }
	),
	new QName (
	    "com.unimas.asn.servicemanager.servicemanagementhttp",
	    "AlarmCode"
	),
	new QName (
	    "ServiceManagementHTTP",
	    "AlarmCode"
	),
	536607,
	null,
	new MemberList (
	    new MemberListElement[] {
		new MemberListElement (
		    "illegalCertificate",
		    1
		),
		new MemberListElement (
		    "deviceException",
		    2
		),
		new MemberListElement (
		    "channelException",
		    3
		),
		new MemberListElement (
		    "protocolVerifyFailure",
		    4
		),
		new MemberListElement (
		    "keywordCheckFailure",
		    5
		)
	    }
	),
	0,
	illegalCertificate
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' AlarmCode object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' AlarmCode object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Methods for "unknownEnumerator"
     */
    private static final AlarmCode cUnknownEnumerator = 
	new AlarmCode(-1);
    
    public boolean isUnknownEnumerator()
    {
	return this == cUnknownEnumerator;
    }
    
    public Enumerated getUnknownEnumerator()
    {
	return cUnknownEnumerator;
    }
    
} // End class definition for AlarmCode
