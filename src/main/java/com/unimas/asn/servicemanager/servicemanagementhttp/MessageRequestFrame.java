/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: fb (Trial), License 88957Z 88957Z. */
/* Abstract syntax: service */
/* ASN.1 Java project: com.unimas.asn.servicemanager.Servicemanager */
/* Created: Tue Jun 10 14:20:32 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.unimas.asn.servicemanager -coer -root -sampleCode pdus
 * -messageFormat msvc D:/history/ASN/service.asn
 */


package com.unimas.asn.servicemanager.servicemanagementhttp;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the MessageRequestFrame ASN.1 type included in the ServiceManagementHTTP ASN.1 module.
 * @see Sequence
 */

public class MessageRequestFrame extends Sequence {
    
    /**
     * The default constructor.
     */
    public MessageRequestFrame()
    {
    }
    
    /**
     * Construct with AbstractData components.
     */
    public MessageRequestFrame(Uint8 version, Content content)
    {
	setVersion(version);
	setContent(content);
    }
    
    public void initComponents()
    {
	mComponents[0] = new Uint8();
	mComponents[1] = new Content();
    }
    
    // Instance initializer
    {
	mComponents = new AbstractData[2];
    }
    
    // Method to create a specific component instance
    public AbstractData createInstance(int index)
    {
	switch (index) {
	    case 0:
		return new Uint8();
	    case 1:
		return new Content();
	    default:
		throw new InternalError("AbstractCollection.createInstance()");
	}
	
    }
    
    
    // Methods for field "version"
    public Uint8 getVersion()
    {
	return (Uint8)mComponents[0];
    }
    
    public void setVersion(Uint8 version)
    {
	mComponents[0] = version;
    }
    
    
    // Methods for field "content"
    public Content getContent()
    {
	return (Content)mComponents[1];
    }
    
    public void setContent(Content content)
    {
	mComponents[1] = content;
    }
    
    
    
    /**
     * Define the Content ASN.1 type included in the ServiceManagementHTTP ASN.1 module.
     * @see Choice
     */
    public static class Content extends Choice {
	
	/**
	 * The default constructor.
	 */
	public Content()
	{
	}
	
	public static final  int  setInterfaceIpRequest_chosen = 1;
	public static final  int  serviceConfigRequest_chosen = 2;
	public static final  int  serviceControlRequest_chosen = 3;
	public static final  int  serviceStatusQueryRequest_chosen = 4;
	public static final  int  serviceConfigQueryRequest_chosen = 5;
	public static final  int  alarmReportRequest_chosen = 6;
	public static final  int  workStatusRequest_chosen = 7;
	public static final  int  getAllServiceIdsRequest_chosen = 8;
	public static final  int  sendPacketStatsRequest_chosen = 9;
	public static final  int  receivePacketStatsRequest_chosen = 10;
	public static final  int  checkCommStatusRequest_chosen = 11;
	public static final  int  queryHostMngRequest_chosen = 12;
	public static final  int  setHostMngRequest_chosen = 13;
	public static final  int  querySourceDeviceRequest_chosen = 14;
	public static final  int  setSourceDeviceRequest_chosen = 15;
	public static final  int  queryRouteListRequest_chosen = 16;
	public static final  int  queryCurRouteRequest_chosen = 17;
	public static final  int  setRouteListRequest_chosen = 18;
	
	// Methods for field "setInterfaceIpRequest"
	public static Content createContentWithSetInterfaceIpRequest(SetInterfaceIpRequest setInterfaceIpRequest)
	{
	    Content __object = new Content();

	    __object.setSetInterfaceIpRequest(setInterfaceIpRequest);
	    return __object;
	}
	
	public boolean hasSetInterfaceIpRequest()
	{
	    return getChosenFlag() == setInterfaceIpRequest_chosen;
	}
	
	public SetInterfaceIpRequest getSetInterfaceIpRequest()
	{
	    if (hasSetInterfaceIpRequest())
		return (SetInterfaceIpRequest)mChosenValue;
	    else
		return null;
	}
	
	public void setSetInterfaceIpRequest(SetInterfaceIpRequest setInterfaceIpRequest)
	{
	    setChosenValue(setInterfaceIpRequest);
	    setChosenFlag(setInterfaceIpRequest_chosen);
	}
	
	
	// Methods for field "serviceConfigRequest"
	public static Content createContentWithServiceConfigRequest(ServiceConfigRequest serviceConfigRequest)
	{
	    Content __object = new Content();

	    __object.setServiceConfigRequest(serviceConfigRequest);
	    return __object;
	}
	
	public boolean hasServiceConfigRequest()
	{
	    return getChosenFlag() == serviceConfigRequest_chosen;
	}
	
	public ServiceConfigRequest getServiceConfigRequest()
	{
	    if (hasServiceConfigRequest())
		return (ServiceConfigRequest)mChosenValue;
	    else
		return null;
	}
	
	public void setServiceConfigRequest(ServiceConfigRequest serviceConfigRequest)
	{
	    setChosenValue(serviceConfigRequest);
	    setChosenFlag(serviceConfigRequest_chosen);
	}
	
	
	// Methods for field "serviceControlRequest"
	public static Content createContentWithServiceControlRequest(ServiceControlRequest serviceControlRequest)
	{
	    Content __object = new Content();

	    __object.setServiceControlRequest(serviceControlRequest);
	    return __object;
	}
	
	public boolean hasServiceControlRequest()
	{
	    return getChosenFlag() == serviceControlRequest_chosen;
	}
	
	public ServiceControlRequest getServiceControlRequest()
	{
	    if (hasServiceControlRequest())
		return (ServiceControlRequest)mChosenValue;
	    else
		return null;
	}
	
	public void setServiceControlRequest(ServiceControlRequest serviceControlRequest)
	{
	    setChosenValue(serviceControlRequest);
	    setChosenFlag(serviceControlRequest_chosen);
	}
	
	
	// Methods for field "serviceStatusQueryRequest"
	public static Content createContentWithServiceStatusQueryRequest(ServiceStatusQueryRequest serviceStatusQueryRequest)
	{
	    Content __object = new Content();

	    __object.setServiceStatusQueryRequest(serviceStatusQueryRequest);
	    return __object;
	}
	
	public boolean hasServiceStatusQueryRequest()
	{
	    return getChosenFlag() == serviceStatusQueryRequest_chosen;
	}
	
	public ServiceStatusQueryRequest getServiceStatusQueryRequest()
	{
	    if (hasServiceStatusQueryRequest())
		return (ServiceStatusQueryRequest)mChosenValue;
	    else
		return null;
	}
	
	public void setServiceStatusQueryRequest(ServiceStatusQueryRequest serviceStatusQueryRequest)
	{
	    setChosenValue(serviceStatusQueryRequest);
	    setChosenFlag(serviceStatusQueryRequest_chosen);
	}
	
	
	// Methods for field "serviceConfigQueryRequest"
	public static Content createContentWithServiceConfigQueryRequest(ServiceConfigQueryRequest serviceConfigQueryRequest)
	{
	    Content __object = new Content();

	    __object.setServiceConfigQueryRequest(serviceConfigQueryRequest);
	    return __object;
	}
	
	public boolean hasServiceConfigQueryRequest()
	{
	    return getChosenFlag() == serviceConfigQueryRequest_chosen;
	}
	
	public ServiceConfigQueryRequest getServiceConfigQueryRequest()
	{
	    if (hasServiceConfigQueryRequest())
		return (ServiceConfigQueryRequest)mChosenValue;
	    else
		return null;
	}
	
	public void setServiceConfigQueryRequest(ServiceConfigQueryRequest serviceConfigQueryRequest)
	{
	    setChosenValue(serviceConfigQueryRequest);
	    setChosenFlag(serviceConfigQueryRequest_chosen);
	}
	
	
	// Methods for field "alarmReportRequest"
	public static Content createContentWithAlarmReportRequest(AlarmReportRequest alarmReportRequest)
	{
	    Content __object = new Content();

	    __object.setAlarmReportRequest(alarmReportRequest);
	    return __object;
	}
	
	public boolean hasAlarmReportRequest()
	{
	    return getChosenFlag() == alarmReportRequest_chosen;
	}
	
	public AlarmReportRequest getAlarmReportRequest()
	{
	    if (hasAlarmReportRequest())
		return (AlarmReportRequest)mChosenValue;
	    else
		return null;
	}
	
	public void setAlarmReportRequest(AlarmReportRequest alarmReportRequest)
	{
	    setChosenValue(alarmReportRequest);
	    setChosenFlag(alarmReportRequest_chosen);
	}
	
	
	// Methods for field "workStatusRequest"
	public static Content createContentWithWorkStatusRequest(WorkStatusRequest workStatusRequest)
	{
	    Content __object = new Content();

	    __object.setWorkStatusRequest(workStatusRequest);
	    return __object;
	}
	
	public boolean hasWorkStatusRequest()
	{
	    return getChosenFlag() == workStatusRequest_chosen;
	}
	
	public WorkStatusRequest getWorkStatusRequest()
	{
	    if (hasWorkStatusRequest())
		return (WorkStatusRequest)mChosenValue;
	    else
		return null;
	}
	
	public void setWorkStatusRequest(WorkStatusRequest workStatusRequest)
	{
	    setChosenValue(workStatusRequest);
	    setChosenFlag(workStatusRequest_chosen);
	}
	
	
	// Methods for field "getAllServiceIdsRequest"
	public static Content createContentWithGetAllServiceIdsRequest(GetAllServiceIdsRequest getAllServiceIdsRequest)
	{
	    Content __object = new Content();

	    __object.setGetAllServiceIdsRequest(getAllServiceIdsRequest);
	    return __object;
	}
	
	public boolean hasGetAllServiceIdsRequest()
	{
	    return getChosenFlag() == getAllServiceIdsRequest_chosen;
	}
	
	public GetAllServiceIdsRequest getGetAllServiceIdsRequest()
	{
	    if (hasGetAllServiceIdsRequest())
		return (GetAllServiceIdsRequest)mChosenValue;
	    else
		return null;
	}
	
	public void setGetAllServiceIdsRequest(GetAllServiceIdsRequest getAllServiceIdsRequest)
	{
	    setChosenValue(getAllServiceIdsRequest);
	    setChosenFlag(getAllServiceIdsRequest_chosen);
	}
	
	
	// Methods for field "sendPacketStatsRequest"
	public static Content createContentWithSendPacketStatsRequest(SendPacketStatsRequest sendPacketStatsRequest)
	{
	    Content __object = new Content();

	    __object.setSendPacketStatsRequest(sendPacketStatsRequest);
	    return __object;
	}
	
	public boolean hasSendPacketStatsRequest()
	{
	    return getChosenFlag() == sendPacketStatsRequest_chosen;
	}
	
	public SendPacketStatsRequest getSendPacketStatsRequest()
	{
	    if (hasSendPacketStatsRequest())
		return (SendPacketStatsRequest)mChosenValue;
	    else
		return null;
	}
	
	public void setSendPacketStatsRequest(SendPacketStatsRequest sendPacketStatsRequest)
	{
	    setChosenValue(sendPacketStatsRequest);
	    setChosenFlag(sendPacketStatsRequest_chosen);
	}
	
	
	// Methods for field "receivePacketStatsRequest"
	public static Content createContentWithReceivePacketStatsRequest(ReceivePacketStatsRequest receivePacketStatsRequest)
	{
	    Content __object = new Content();

	    __object.setReceivePacketStatsRequest(receivePacketStatsRequest);
	    return __object;
	}
	
	public boolean hasReceivePacketStatsRequest()
	{
	    return getChosenFlag() == receivePacketStatsRequest_chosen;
	}
	
	public ReceivePacketStatsRequest getReceivePacketStatsRequest()
	{
	    if (hasReceivePacketStatsRequest())
		return (ReceivePacketStatsRequest)mChosenValue;
	    else
		return null;
	}
	
	public void setReceivePacketStatsRequest(ReceivePacketStatsRequest receivePacketStatsRequest)
	{
	    setChosenValue(receivePacketStatsRequest);
	    setChosenFlag(receivePacketStatsRequest_chosen);
	}
	
	
	// Methods for field "checkCommStatusRequest"
	public static Content createContentWithCheckCommStatusRequest(CheckCommStatusRequest checkCommStatusRequest)
	{
	    Content __object = new Content();

	    __object.setCheckCommStatusRequest(checkCommStatusRequest);
	    return __object;
	}
	
	public boolean hasCheckCommStatusRequest()
	{
	    return getChosenFlag() == checkCommStatusRequest_chosen;
	}
	
	public CheckCommStatusRequest getCheckCommStatusRequest()
	{
	    if (hasCheckCommStatusRequest())
		return (CheckCommStatusRequest)mChosenValue;
	    else
		return null;
	}
	
	public void setCheckCommStatusRequest(CheckCommStatusRequest checkCommStatusRequest)
	{
	    setChosenValue(checkCommStatusRequest);
	    setChosenFlag(checkCommStatusRequest_chosen);
	}
	
	
	// Methods for field "queryHostMngRequest"
	public static Content createContentWithQueryHostMngRequest(QueryHostMngRequest queryHostMngRequest)
	{
	    Content __object = new Content();

	    __object.setQueryHostMngRequest(queryHostMngRequest);
	    return __object;
	}
	
	public boolean hasQueryHostMngRequest()
	{
	    return getChosenFlag() == queryHostMngRequest_chosen;
	}
	
	public QueryHostMngRequest getQueryHostMngRequest()
	{
	    if (hasQueryHostMngRequest())
		return (QueryHostMngRequest)mChosenValue;
	    else
		return null;
	}
	
	public void setQueryHostMngRequest(QueryHostMngRequest queryHostMngRequest)
	{
	    setChosenValue(queryHostMngRequest);
	    setChosenFlag(queryHostMngRequest_chosen);
	}
	
	
	// Methods for field "setHostMngRequest"
	public static Content createContentWithSetHostMngRequest(SetHostMngRequest setHostMngRequest)
	{
	    Content __object = new Content();

	    __object.setSetHostMngRequest(setHostMngRequest);
	    return __object;
	}
	
	public boolean hasSetHostMngRequest()
	{
	    return getChosenFlag() == setHostMngRequest_chosen;
	}
	
	public SetHostMngRequest getSetHostMngRequest()
	{
	    if (hasSetHostMngRequest())
		return (SetHostMngRequest)mChosenValue;
	    else
		return null;
	}
	
	public void setSetHostMngRequest(SetHostMngRequest setHostMngRequest)
	{
	    setChosenValue(setHostMngRequest);
	    setChosenFlag(setHostMngRequest_chosen);
	}
	
	
	// Methods for field "querySourceDeviceRequest"
	public static Content createContentWithQuerySourceDeviceRequest(QuerySourceDeviceRequest querySourceDeviceRequest)
	{
	    Content __object = new Content();

	    __object.setQuerySourceDeviceRequest(querySourceDeviceRequest);
	    return __object;
	}
	
	public boolean hasQuerySourceDeviceRequest()
	{
	    return getChosenFlag() == querySourceDeviceRequest_chosen;
	}
	
	public QuerySourceDeviceRequest getQuerySourceDeviceRequest()
	{
	    if (hasQuerySourceDeviceRequest())
		return (QuerySourceDeviceRequest)mChosenValue;
	    else
		return null;
	}
	
	public void setQuerySourceDeviceRequest(QuerySourceDeviceRequest querySourceDeviceRequest)
	{
	    setChosenValue(querySourceDeviceRequest);
	    setChosenFlag(querySourceDeviceRequest_chosen);
	}
	
	
	// Methods for field "setSourceDeviceRequest"
	public static Content createContentWithSetSourceDeviceRequest(SetSourceDeviceRequest setSourceDeviceRequest)
	{
	    Content __object = new Content();

	    __object.setSetSourceDeviceRequest(setSourceDeviceRequest);
	    return __object;
	}
	
	public boolean hasSetSourceDeviceRequest()
	{
	    return getChosenFlag() == setSourceDeviceRequest_chosen;
	}
	
	public SetSourceDeviceRequest getSetSourceDeviceRequest()
	{
	    if (hasSetSourceDeviceRequest())
		return (SetSourceDeviceRequest)mChosenValue;
	    else
		return null;
	}
	
	public void setSetSourceDeviceRequest(SetSourceDeviceRequest setSourceDeviceRequest)
	{
	    setChosenValue(setSourceDeviceRequest);
	    setChosenFlag(setSourceDeviceRequest_chosen);
	}
	
	
	// Methods for field "queryRouteListRequest"
	public static Content createContentWithQueryRouteListRequest(QueryRouteListRequest queryRouteListRequest)
	{
	    Content __object = new Content();

	    __object.setQueryRouteListRequest(queryRouteListRequest);
	    return __object;
	}
	
	public boolean hasQueryRouteListRequest()
	{
	    return getChosenFlag() == queryRouteListRequest_chosen;
	}
	
	public QueryRouteListRequest getQueryRouteListRequest()
	{
	    if (hasQueryRouteListRequest())
		return (QueryRouteListRequest)mChosenValue;
	    else
		return null;
	}
	
	public void setQueryRouteListRequest(QueryRouteListRequest queryRouteListRequest)
	{
	    setChosenValue(queryRouteListRequest);
	    setChosenFlag(queryRouteListRequest_chosen);
	}
	
	
	// Methods for field "queryCurRouteRequest"
	public static Content createContentWithQueryCurRouteRequest(QueryCurRouteRequest queryCurRouteRequest)
	{
	    Content __object = new Content();

	    __object.setQueryCurRouteRequest(queryCurRouteRequest);
	    return __object;
	}
	
	public boolean hasQueryCurRouteRequest()
	{
	    return getChosenFlag() == queryCurRouteRequest_chosen;
	}
	
	public QueryCurRouteRequest getQueryCurRouteRequest()
	{
	    if (hasQueryCurRouteRequest())
		return (QueryCurRouteRequest)mChosenValue;
	    else
		return null;
	}
	
	public void setQueryCurRouteRequest(QueryCurRouteRequest queryCurRouteRequest)
	{
	    setChosenValue(queryCurRouteRequest);
	    setChosenFlag(queryCurRouteRequest_chosen);
	}
	
	
	// Methods for field "setRouteListRequest"
	public static Content createContentWithSetRouteListRequest(SetRouteListRequest setRouteListRequest)
	{
	    Content __object = new Content();

	    __object.setSetRouteListRequest(setRouteListRequest);
	    return __object;
	}
	
	public boolean hasSetRouteListRequest()
	{
	    return getChosenFlag() == setRouteListRequest_chosen;
	}
	
	public SetRouteListRequest getSetRouteListRequest()
	{
	    if (hasSetRouteListRequest())
		return (SetRouteListRequest)mChosenValue;
	    else
		return null;
	}
	
	public void setSetRouteListRequest(SetRouteListRequest setRouteListRequest)
	{
	    setChosenValue(setRouteListRequest);
	    setChosenFlag(setRouteListRequest_chosen);
	}
	
	
	// Method to create a specific choice instance
	public AbstractData createInstance(int chosen)
	{
	    switch (chosen) {
		case setInterfaceIpRequest_chosen:
		    return new SetInterfaceIpRequest();
		case serviceConfigRequest_chosen:
		    return new ServiceConfigRequest();
		case serviceControlRequest_chosen:
		    return new ServiceControlRequest();
		case serviceStatusQueryRequest_chosen:
		    return new ServiceStatusQueryRequest();
		case serviceConfigQueryRequest_chosen:
		    return new ServiceConfigQueryRequest();
		case alarmReportRequest_chosen:
		    return new AlarmReportRequest();
		case workStatusRequest_chosen:
		    return new WorkStatusRequest();
		case getAllServiceIdsRequest_chosen:
		    return new GetAllServiceIdsRequest();
		case sendPacketStatsRequest_chosen:
		    return new SendPacketStatsRequest();
		case receivePacketStatsRequest_chosen:
		    return new ReceivePacketStatsRequest();
		case checkCommStatusRequest_chosen:
		    return new CheckCommStatusRequest();
		case queryHostMngRequest_chosen:
		    return new QueryHostMngRequest();
		case setHostMngRequest_chosen:
		    return new SetHostMngRequest();
		case querySourceDeviceRequest_chosen:
		    return new QuerySourceDeviceRequest();
		case setSourceDeviceRequest_chosen:
		    return new SetSourceDeviceRequest();
		case queryRouteListRequest_chosen:
		    return new QueryRouteListRequest();
		case queryCurRouteRequest_chosen:
		    return new QueryCurRouteRequest();
		case setRouteListRequest_chosen:
		    return new SetRouteListRequest();
		default:
		    throw new InternalError("Choice.createInstance()");
	    }
	    
	}
	
	/**
	 * Initialize the type descriptor.
	 */
	private static final ChoiceInfo c_typeinfo = new ChoiceInfo (
	    new Tags (
		new short[] {
		    (short)0x8001
		}
	    ),
	    new QName (
		"com.unimas.asn.servicemanager.servicemanagementhttp",
		"MessageRequestFrame$Content"
	    ),
	    new QName (
		"builtin",
		"CHOICE"
	    ),
	    536607,
	    null,
	    new FieldsList (
		new FieldInfo[] {
		    new FieldInfo (
			new TypeInfoRef (
			    new SequenceInfo (
				new Tags (
				    new short[] {
					(short)0x8000
				    }
				),
				new QName (
				    "com.unimas.asn.servicemanager.servicemanagementhttp",
				    "SetInterfaceIpRequest"
				),
				new QName (
				    "ServiceManagementHTTP",
				    "SetInterfaceIpRequest"
				),
				536603,
				null,
				new FieldsRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"SetInterfaceIpRequest"
				    )
				),
				0,
				new TagDecodersRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"SetInterfaceIpRequest"
				    )
				),
				0
			    )
			),
			"setInterfaceIpRequest",
			0,
			2
		    ),
		    new FieldInfo (
			new TypeInfoRef (
			    new ChoiceInfo (
				new Tags (
				    new short[] {
					(short)0x8001
				    }
				),
				new QName (
				    "com.unimas.asn.servicemanager.servicemanagementhttp",
				    "ServiceConfigRequest"
				),
				new QName (
				    "ServiceManagementHTTP",
				    "ServiceConfigRequest"
				),
				536607,
				null,
				new FieldsRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"ServiceConfigRequest"
				    )
				),
				0,
				new TagDecoder (
				    new TagDecoderElement[] {
					new TagDecoderElement((short)0x8000, 0),
					new TagDecoderElement((short)0x8001, 1),
					new TagDecoderElement((short)0x8002, 2)
				    }
				)
			    )
			),
			"serviceConfigRequest",
			1,
			2
		    ),
		    new FieldInfo (
			new TypeInfoRef (
			    new SequenceInfo (
				new Tags (
				    new short[] {
					(short)0x8002
				    }
				),
				new QName (
				    "com.unimas.asn.servicemanager.servicemanagementhttp",
				    "ServiceControlRequest"
				),
				new QName (
				    "ServiceManagementHTTP",
				    "ServiceControlRequest"
				),
				536607,
				null,
				new FieldsRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"ServiceControlRequest"
				    )
				),
				0,
				new TagDecodersRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"ServiceControlRequest"
				    )
				),
				0
			    )
			),
			"serviceControlRequest",
			2,
			2
		    ),
		    new FieldInfo (
			new TypeInfoRef (
			    new SequenceInfo (
				new Tags (
				    new short[] {
					(short)0x8003
				    }
				),
				new QName (
				    "com.unimas.asn.servicemanager.servicemanagementhttp",
				    "ServiceStatusQueryRequest"
				),
				new QName (
				    "ServiceManagementHTTP",
				    "ServiceStatusQueryRequest"
				),
				536607,
				null,
				new FieldsRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"ServiceStatusQueryRequest"
				    )
				),
				0,
				new TagDecodersRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"ServiceStatusQueryRequest"
				    )
				),
				0
			    )
			),
			"serviceStatusQueryRequest",
			3,
			2
		    ),
		    new FieldInfo (
			new TypeInfoRef (
			    new SequenceInfo (
				new Tags (
				    new short[] {
					(short)0x8004
				    }
				),
				new QName (
				    "com.unimas.asn.servicemanager.servicemanagementhttp",
				    "ServiceConfigQueryRequest"
				),
				new QName (
				    "ServiceManagementHTTP",
				    "ServiceConfigQueryRequest"
				),
				536607,
				null,
				new FieldsRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"ServiceConfigQueryRequest"
				    )
				),
				0,
				new TagDecodersRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"ServiceConfigQueryRequest"
				    )
				),
				0
			    )
			),
			"serviceConfigQueryRequest",
			4,
			2
		    ),
		    new FieldInfo (
			new TypeInfoRef (
			    new SequenceInfo (
				new Tags (
				    new short[] {
					(short)0x8005
				    }
				),
				new QName (
				    "com.unimas.asn.servicemanager.servicemanagementhttp",
				    "AlarmReportRequest"
				),
				new QName (
				    "ServiceManagementHTTP",
				    "AlarmReportRequest"
				),
				536607,
				null,
				new FieldsRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"AlarmReportRequest"
				    )
				),
				0,
				new TagDecodersRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"AlarmReportRequest"
				    )
				),
				0
			    )
			),
			"alarmReportRequest",
			5,
			2
		    ),
		    new FieldInfo (
			new TypeInfoRef (
			    new SequenceInfo (
				new Tags (
				    new short[] {
					(short)0x8006
				    }
				),
				new QName (
				    "com.unimas.asn.servicemanager.servicemanagementhttp",
				    "WorkStatusRequest"
				),
				new QName (
				    "ServiceManagementHTTP",
				    "WorkStatusRequest"
				),
				536607,
				null,
				new FieldsRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"WorkStatusRequest"
				    )
				),
				0,
				new TagDecodersRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"WorkStatusRequest"
				    )
				),
				0
			    )
			),
			"workStatusRequest",
			6,
			2
		    ),
		    new FieldInfo (
			new TypeInfoRef (
			    new SequenceInfo (
				new Tags (
				    new short[] {
					(short)0x8007
				    }
				),
				new QName (
				    "com.unimas.asn.servicemanager.servicemanagementhttp",
				    "GetAllServiceIdsRequest"
				),
				new QName (
				    "ServiceManagementHTTP",
				    "GetAllServiceIdsRequest"
				),
				536603,
				null,
				new FieldsRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"GetAllServiceIdsRequest"
				    )
				),
				0,
				new TagDecodersRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"GetAllServiceIdsRequest"
				    )
				),
				0
			    )
			),
			"getAllServiceIdsRequest",
			7,
			2
		    ),
		    new FieldInfo (
			new TypeInfoRef (
			    new SequenceInfo (
				new Tags (
				    new short[] {
					(short)0x8008
				    }
				),
				new QName (
				    "com.unimas.asn.servicemanager.servicemanagementhttp",
				    "SendPacketStatsRequest"
				),
				new QName (
				    "ServiceManagementHTTP",
				    "SendPacketStatsRequest"
				),
				536603,
				null,
				new FieldsRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"SendPacketStatsRequest"
				    )
				),
				0,
				new TagDecodersRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"SendPacketStatsRequest"
				    )
				),
				0
			    )
			),
			"sendPacketStatsRequest",
			8,
			2
		    ),
		    new FieldInfo (
			new TypeInfoRef (
			    new SequenceInfo (
				new Tags (
				    new short[] {
					(short)0x8009
				    }
				),
				new QName (
				    "com.unimas.asn.servicemanager.servicemanagementhttp",
				    "ReceivePacketStatsRequest"
				),
				new QName (
				    "ServiceManagementHTTP",
				    "ReceivePacketStatsRequest"
				),
				536603,
				null,
				new FieldsRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"ReceivePacketStatsRequest"
				    )
				),
				0,
				new TagDecodersRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"ReceivePacketStatsRequest"
				    )
				),
				0
			    )
			),
			"receivePacketStatsRequest",
			9,
			2
		    ),
		    new FieldInfo (
			new TypeInfoRef (
			    new SequenceInfo (
				new Tags (
				    new short[] {
					(short)0x800a
				    }
				),
				new QName (
				    "com.unimas.asn.servicemanager.servicemanagementhttp",
				    "CheckCommStatusRequest"
				),
				new QName (
				    "ServiceManagementHTTP",
				    "CheckCommStatusRequest"
				),
				536603,
				null,
				new FieldsRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"CheckCommStatusRequest"
				    )
				),
				0,
				new TagDecodersRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"CheckCommStatusRequest"
				    )
				),
				0
			    )
			),
			"checkCommStatusRequest",
			10,
			2
		    ),
		    new FieldInfo (
			new TypeInfoRef (
			    new SequenceInfo (
				new Tags (
				    new short[] {
					(short)0x800b
				    }
				),
				new QName (
				    "com.unimas.asn.servicemanager.servicemanagementhttp",
				    "QueryHostMngRequest"
				),
				new QName (
				    "ServiceManagementHTTP",
				    "QueryHostMngRequest"
				),
				536603,
				null,
				new FieldsRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"QueryHostMngRequest"
				    )
				),
				0,
				new TagDecodersRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"QueryHostMngRequest"
				    )
				),
				0
			    )
			),
			"queryHostMngRequest",
			11,
			2
		    ),
		    new FieldInfo (
			new TypeInfoRef (
			    new SequenceInfo (
				new Tags (
				    new short[] {
					(short)0x800c
				    }
				),
				new QName (
				    "com.unimas.asn.servicemanager.servicemanagementhttp",
				    "SetHostMngRequest"
				),
				new QName (
				    "ServiceManagementHTTP",
				    "SetHostMngRequest"
				),
				536603,
				null,
				new FieldsRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"SetHostMngRequest"
				    )
				),
				0,
				new TagDecodersRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"SetHostMngRequest"
				    )
				),
				0
			    )
			),
			"setHostMngRequest",
			12,
			2
		    ),
		    new FieldInfo (
			new TypeInfoRef (
			    new SequenceInfo (
				new Tags (
				    new short[] {
					(short)0x800d
				    }
				),
				new QName (
				    "com.unimas.asn.servicemanager.servicemanagementhttp",
				    "QuerySourceDeviceRequest"
				),
				new QName (
				    "ServiceManagementHTTP",
				    "QuerySourceDeviceRequest"
				),
				536603,
				null,
				new FieldsRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"QuerySourceDeviceRequest"
				    )
				),
				0,
				new TagDecodersRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"QuerySourceDeviceRequest"
				    )
				),
				0
			    )
			),
			"querySourceDeviceRequest",
			13,
			2
		    ),
		    new FieldInfo (
			new TypeInfoRef (
			    new SequenceInfo (
				new Tags (
				    new short[] {
					(short)0x800e
				    }
				),
				new QName (
				    "com.unimas.asn.servicemanager.servicemanagementhttp",
				    "SetSourceDeviceRequest"
				),
				new QName (
				    "ServiceManagementHTTP",
				    "SetSourceDeviceRequest"
				),
				536603,
				null,
				new FieldsRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"SetSourceDeviceRequest"
				    )
				),
				0,
				new TagDecodersRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"SetSourceDeviceRequest"
				    )
				),
				0
			    )
			),
			"setSourceDeviceRequest",
			14,
			2
		    ),
		    new FieldInfo (
			new TypeInfoRef (
			    new SequenceInfo (
				new Tags (
				    new short[] {
					(short)0x800f
				    }
				),
				new QName (
				    "com.unimas.asn.servicemanager.servicemanagementhttp",
				    "QueryRouteListRequest"
				),
				new QName (
				    "ServiceManagementHTTP",
				    "QueryRouteListRequest"
				),
				536603,
				null,
				new FieldsRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"QueryRouteListRequest"
				    )
				),
				0,
				new TagDecodersRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"QueryRouteListRequest"
				    )
				),
				0
			    )
			),
			"queryRouteListRequest",
			15,
			2
		    ),
		    new FieldInfo (
			new TypeInfoRef (
			    new SequenceInfo (
				new Tags (
				    new short[] {
					(short)0x8010
				    }
				),
				new QName (
				    "com.unimas.asn.servicemanager.servicemanagementhttp",
				    "QueryCurRouteRequest"
				),
				new QName (
				    "ServiceManagementHTTP",
				    "QueryCurRouteRequest"
				),
				536603,
				null,
				new FieldsRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"QueryCurRouteRequest"
				    )
				),
				0,
				new TagDecodersRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"QueryCurRouteRequest"
				    )
				),
				0
			    )
			),
			"queryCurRouteRequest",
			16,
			2
		    ),
		    new FieldInfo (
			new TypeInfoRef (
			    new SequenceInfo (
				new Tags (
				    new short[] {
					(short)0x8011
				    }
				),
				new QName (
				    "com.unimas.asn.servicemanager.servicemanagementhttp",
				    "SetRouteListRequest"
				),
				new QName (
				    "ServiceManagementHTTP",
				    "SetRouteListRequest"
				),
				536603,
				null,
				new FieldsRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"SetRouteListRequest"
				    )
				),
				0,
				new TagDecodersRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"SetRouteListRequest"
				    )
				),
				0
			    )
			),
			"setRouteListRequest",
			17,
			2
		    )
		}
	    ),
	    0,
	    new TagDecoder (
		new TagDecoderElement[] {
		    new TagDecoderElement((short)0x8000, 0),
		    new TagDecoderElement((short)0x8001, 1),
		    new TagDecoderElement((short)0x8002, 2),
		    new TagDecoderElement((short)0x8003, 3),
		    new TagDecoderElement((short)0x8004, 4),
		    new TagDecoderElement((short)0x8005, 5),
		    new TagDecoderElement((short)0x8006, 6),
		    new TagDecoderElement((short)0x8007, 7),
		    new TagDecoderElement((short)0x8008, 8),
		    new TagDecoderElement((short)0x8009, 9),
		    new TagDecoderElement((short)0x800a, 10),
		    new TagDecoderElement((short)0x800b, 11),
		    new TagDecoderElement((short)0x800c, 12),
		    new TagDecoderElement((short)0x800d, 13),
		    new TagDecoderElement((short)0x800e, 14),
		    new TagDecoderElement((short)0x800f, 15),
		    new TagDecoderElement((short)0x8010, 16),
		    new TagDecoderElement((short)0x8011, 17)
		}
	    )
	);
	
	/**
	 * Get the type descriptor (TypeInfo) of 'this' Content object.
	 */
	public TypeInfo getTypeInfo()
	{
	    return c_typeinfo;
	}
	
	/**
	 * Get the static type descriptor (TypeInfo) of 'this' Content object.
	 */
	public static TypeInfo getStaticTypeInfo()
	{
	    return c_typeinfo;
	}
	
	/**
	 * Check the current selection on unknown extension
	 */
	public final boolean hasUnknownExtension()
	{
	    return getChosenFlag() > 18;
	}
	
    } // End class definition for Content

    /**
     * Initialize the type descriptor.
     */
    private static final SequenceInfo c_typeinfo = new SequenceInfo (
	new Tags (
	    new short[] {
		0x0010
	    }
	),
	new QName (
	    "com.unimas.asn.servicemanager.servicemanagementhttp",
	    "MessageRequestFrame"
	),
	new QName (
	    "ServiceManagementHTTP",
	    "MessageRequestFrame"
	),
	536603,
	null,
	new FieldsList (
	    new SequenceFieldInfo[] {
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new IntegerInfo (
			    new Tags (
				new short[] {
				    (short)0x8000
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"Uint8"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"Uint8"
			    ),
			    536603,
			    new Intersection (
				new ValueRangeConstraint (
				    new AbstractBounds(
					new Uint8(0), 
					new Uint8(255),
					0
				    )
				),
				new SingleValueConstraint (
				    new Uint8(1)
				)
			    ),
			    new Bounds (
				java.lang.Long.valueOf(1),
				java.lang.Long.valueOf(1)
			    ),
			    null,
			    1
			)
		    ),
		    "version",
		    0,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new QName (
			    "com.unimas.asn.servicemanager.servicemanagementhttp",
			    "MessageRequestFrame$Content"
			)
		    ),
		    "content",
		    1,
		    2,
		    null
		)
	    }
	),
	0,
	new TagDecoders (
	    new TagDecoder[] {
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8000, 0)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8001, 1)
		    }
		)
	    }
	),
	0
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' MessageRequestFrame object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' MessageRequestFrame object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * The type is a PDU.
     */
    public boolean isPDU()
    {
	return true;
    }
    
} // End class definition for MessageRequestFrame
