/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: fb (Trial), License 88957Z 88957Z. */
/* Abstract syntax: service */
/* ASN.1 Java project: com.unimas.asn.servicemanager.Servicemanager */
/* Created: <PERSON>e Jun 10 14:20:32 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.unimas.asn.servicemanager -coer -root -sampleCode pdus
 * -messageFormat msvc D:/history/ASN/service.asn
 */


package com.unimas.asn.servicemanager.servicemanagementhttp;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the Float ASN.1 type included in the ServiceManagementHTTP ASN.1 module.
 * @see Real
 */

public class Float extends Real {
    
    /**
     * The default constructor.
     */
    public Float()
    {
    }
    
    /**
     * Construct from a float type.
     * @param value the float object to set this object to.
     */
    public Float(float value)
    {
	super(value);
    }
    
    /**
     * Construct from a double type.
     * @param value the double object to set this object to.
     */
    public Float(double value)
    {
	super(value);
    }
    
    /**
     * Initialize the type descriptor.
     */
    private static final RealInfo c_typeinfo = new RealInfo (
	new Tags (
	    new short[] {
		0x0009
	    }
	),
	new QName (
	    "com.unimas.asn.servicemanager.servicemanagementhttp",
	    "Float"
	),
	new QName (
	    "ServiceManagementHTTP",
	    "Float"
	),
	536603,
	null,
	4,
	0
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' com.unimas.asn.servicemanager.servicemanagementhttp.Float object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' com.unimas.asn.servicemanager.servicemanagementhttp.Float object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * The type is a PDU.
     */
    public boolean isPDU()
    {
	return true;
    }
    
} // End class definition for Float
