package com.unimas.asn.logic;

import com.unimas.asn.servicemanager.servicemanagementhttp.*;
import com.unimas.asn.util.IPUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

/**
 * 设置路由列表逻辑处理类
 * 负责全覆盖式设置指定网卡的路由配置
 */
public class SetRouteListLogic {
    private static final Logger logger = LoggerFactory.getLogger(SetRouteListLogic.class);
    private static final String ROUTE_SCRIPT_PATH = "/etc/unimas/tomcat/conf/route.sh";
    
    /**
     * 设置路由列表
     * @param request 设置路由列表请求
     * @return 设置路由列表响应
     */
    public SetRouteListResponse setRouteList(SetRouteListRequest request) {
        logger.info("Processing SetRouteListRequest for interface: {}", request.getInterfaceType());
        
        // 创建响应对象
        SetRouteListResponse response = new SetRouteListResponse();
        response.setMessageType(ContentMessageType.iprouteService);
        response.setInterfaceType(request.getInterfaceType());
        
        try {
            // 确定目标网卡名称
            String targetInterface = getInterfaceName(request.getInterfaceType());

            // 1. 备份现有路由（以防需要恢复）
            List<String> existingRoutes = backupExistingRoutes(targetInterface);

            // 2. 先尝试添加新路由到系统，验证有效性（不删除原有路由）
            List<RouteList> successfulRoutes = addRoutesToSystem(request.getRouteList(), targetInterface);

            // 3. 只有至少有一个路由成功时，才进行全覆盖操作
            if (successfulRoutes.size() > 0) {
                // 删除该网卡的所有现有路由（从系统和配置文件）
                removeExistingRoutes(targetInterface, existingRoutes);

                // 重新添加成功验证的路由到系统（因为上面删除了）
                reapplySuccessfulRoutes(successfulRoutes, targetInterface);

                // 更新配置文件
                updateRouteConfigFile(successfulRoutes, targetInterface);

                logger.info("Successfully set route list for interface: {}, {} routes configured",
                           request.getInterfaceType(), successfulRoutes.size());
            } else {
                // 清理测试时可能添加的路由
                cleanupTestRoutes(request.getRouteList(), targetInterface);

                logger.warn("Failed to add any new routes, keeping existing routes for interface: {}",
                           request.getInterfaceType());
            }

            // 3. 创建响应中的路由列表
            SetRouteListResponse.RouteList responseRouteList = new SetRouteListResponse.RouteList();
            for (RouteList route : successfulRoutes) {
                responseRouteList.add(route);
            }
            response.setRouteList(responseRouteList);

            // 4. 设置执行结果
            if (successfulRoutes.size() > 0) {
                response.setResult(0); // 成功（至少有一条生效）
            } else {
                response.setResult(1); // 失败（所有路由都失败）
            }
            
        } catch (Exception e) {
            logger.error("Failed to set route list for interface: {}", request.getInterfaceType(), e);
            
            // 创建空的路由列表
            SetRouteListResponse.RouteList emptyRouteList = new SetRouteListResponse.RouteList();
            response.setRouteList(emptyRouteList);
            response.setResult(1); // 失败
        }
        
        return response;
    }

    /**
     * 备份配置文件中现有的路由信息
     * @param targetInterface 目标网卡名称
     * @return 现有路由命令列表
     */
    private List<String> backupExistingRoutes(String targetInterface) {
        List<String> existingRoutes = new ArrayList<>();

        try {
            logger.info("Backing up existing config routes for interface: {}", targetInterface);

            Path scriptPath = Paths.get(ROUTE_SCRIPT_PATH);
            if (!Files.exists(scriptPath)) {
                logger.info("Route script file does not exist, no routes to backup: {}", ROUTE_SCRIPT_PATH);
                return existingRoutes;
            }

            // 读取配置文件内容
            List<String> lines = Files.readAllLines(scriptPath);

            // 查找目标网卡的路由
            for (String line : lines) {
                line = line.trim();

                // 跳过注释和空行
                if (line.isEmpty() || line.startsWith("#")) {
                    continue;
                }

                // 检查是否为目标网卡的路由
                if (line.contains("dev " + targetInterface)) {
                    existingRoutes.add(line);
                    logger.debug("Backed up config route: {}", line);
                }
            }

            logger.info("Backed up {} existing config routes for interface: {}", existingRoutes.size(), targetInterface);

        } catch (Exception e) {
            logger.error("Failed to backup existing config routes for interface: {}", targetInterface, e);
        }

        return existingRoutes;
    }

    /**
     * 清理测试时添加的路由
     * @param routeList 测试的路由列表
     * @param targetInterface 目标网卡名称
     */
    private void cleanupTestRoutes(SetRouteListRequest.RouteList routeList, String targetInterface) {
        if (routeList == null || routeList.size() == 0) {
            return;
        }

        logger.info("Cleaning up test routes for interface: {}", targetInterface);

        for (int i = 0; i < routeList.size(); i++) {
            RouteList route = routeList.get(i);

            try {
                String routeCommand = buildRouteCommand(route, targetInterface);
                String networkPart = extractNetworkPart(routeCommand);
                String tableName = getTableName(targetInterface);
                String deleteCommand = "ip route del " + networkPart + " table " + tableName;

                // 尝试删除测试时可能添加的路由
                executeRouteCommand(deleteCommand);
                logger.debug("Cleaned up test route: {}", deleteCommand);

            } catch (Exception e) {
                logger.debug("Failed to cleanup test route {}: {}", i, e.getMessage());
            }
        }

        logger.info("Completed cleanup of test routes for interface: {}", targetInterface);
    }

    /**
     * 删除指定网卡的所有现有路由（从系统和配置文件）
     * @param targetInterface 目标网卡名称
     * @param existingRoutes 现有的路由列表
     * @throws IOException 文件操作异常
     */
    private void removeExistingRoutes(String targetInterface, List<String> existingRoutes) throws IOException {
        // 1. 先删除系统中的现有路由
        removeSystemRoutes(targetInterface, existingRoutes);

        // 2. 再删除配置文件中的路由
        removeConfigFileRoutes(targetInterface);
    }

    /**
     * 删除系统中配置文件管理的路由
     * @param targetInterface 目标网卡名称
     * @param configRoutes 配置文件中的路由列表
     */
    private void removeSystemRoutes(String targetInterface, List<String> configRoutes) {
        if (configRoutes.isEmpty()) {
            logger.info("No config routes to remove from system for interface: {}", targetInterface);
            return;
        }

        logger.info("Removing {} config routes from system for interface: {}", configRoutes.size(), targetInterface);

        // 删除配置文件中的路由
        String tableName = getTableName(targetInterface);
        for (String routeCommand : configRoutes) {
            try {
                // 提取网络部分，只删除网络，不指定网关
                String networkPart = extractNetworkPart(routeCommand);
                String deleteCommand = "ip route del " + networkPart + " table " + tableName;

                logger.debug("Executing: {}", deleteCommand);
                ProcessBuilder deletePb = new ProcessBuilder("bash", "-c", deleteCommand);
                Process deleteProcess = deletePb.start();
                int exitCode = deleteProcess.waitFor();

                if (exitCode == 0) {
                    logger.debug("Successfully deleted config route: {}", deleteCommand);
                } else {
                    logger.warn("Failed to delete config route: {}, exit code: {}", deleteCommand, exitCode);
                }

            } catch (Exception e) {
                logger.warn("Error deleting config route: {}", routeCommand, e);
            }
        }

        logger.info("Completed removing config routes from system for interface: {}", targetInterface);
    }

    /**
     * 删除配置文件中指定网卡的路由
     * @param targetInterface 目标网卡名称
     * @throws IOException 文件操作异常
     */
    private void removeConfigFileRoutes(String targetInterface) throws IOException {
        Path scriptPath = Paths.get(ROUTE_SCRIPT_PATH);

        if (!Files.exists(scriptPath)) {
            logger.info("Route script file does not exist, will create when needed: {}", ROUTE_SCRIPT_PATH);
            // 创建目录（如果不存在）
            Path scriptDir = scriptPath.getParent();
            if (!Files.exists(scriptDir)) {
                Files.createDirectories(scriptDir);
            }
            return;
        }

        // 读取现有文件内容
        List<String> lines = Files.readAllLines(scriptPath);
        List<String> filteredLines = new ArrayList<>();

        // 过滤掉目标网卡的路由，保留其他网卡的路由
        for (String line : lines) {
            line = line.trim();

            // 保留注释和空行
            if (line.isEmpty() || line.startsWith("#")) {
                filteredLines.add(line);
                continue;
            }

            // 检查是否为目标网卡的路由
            if (!line.contains("dev " + targetInterface)) {
                filteredLines.add(line);
            } else {
                logger.debug("Removing config file route for interface {}: {}", targetInterface, line);
            }
        }

        // 写回文件
        Files.write(scriptPath, filteredLines);
        logger.info("Removed config file routes for interface: {}", targetInterface);
    }
    
    /**
     * 先添加路由到系统，验证成功后返回有效路由列表
     * @param routeList 路由列表
     * @param targetInterface 目标网卡名称
     * @return 成功添加到系统的路由列表
     */
    private List<RouteList> addRoutesToSystem(SetRouteListRequest.RouteList routeList, String targetInterface) {
        List<RouteList> successfulRoutes = new ArrayList<>();

        if (routeList == null || routeList.size() == 0) {
            logger.info("No routes to add for interface: {}", targetInterface);
            return successfulRoutes;
        }

        logger.info("Testing {} routes on system for interface: {}", routeList.size(), targetInterface);

        // 逐个测试添加路由到系统
        for (int i = 0; i < routeList.size(); i++) {
            RouteList route = routeList.get(i);

            try {
                String routeCommand = buildRouteCommand(route, targetInterface);

                // 先尝试删除可能存在的相同网络路由，避免冲突
                String networkPart = extractNetworkPart(routeCommand);
                String tableName = getTableName(targetInterface);
                String deleteCommand = "ip route del " + networkPart + " table " + tableName;
                executeRouteCommand(deleteCommand); // 忽略删除结果，可能本来就不存在

                // 执行添加路由命令
                if (executeRouteCommand(routeCommand)) {
                    successfulRoutes.add(route);
                    logger.info("Successfully tested route on system: {}", routeCommand);
                } else {
                    logger.warn("Failed to add route to system: {}", routeCommand);
                }

            } catch (Exception e) {
                logger.warn("Failed to build route command for route {}: {}", i, e.getMessage());
            }
        }

        logger.info("Successfully added {} out of {} routes to system for interface: {}",
                   successfulRoutes.size(), routeList.size(), targetInterface);

        return successfulRoutes;
    }

    /**
     * 重新应用已验证成功的路由到系统
     * @param successfulRoutes 已验证成功的路由列表
     * @param targetInterface 目标网卡名称
     */
    private void reapplySuccessfulRoutes(List<RouteList> successfulRoutes, String targetInterface) {
        logger.info("Reapplying {} verified routes to system for interface: {}",
                   successfulRoutes.size(), targetInterface);

        for (RouteList route : successfulRoutes) {
            try {
                String routeCommand = buildRouteCommand(route, targetInterface);

                if (executeRouteCommand(routeCommand)) {
                    logger.debug("Successfully reapplied route: {}", routeCommand);
                } else {
                    logger.warn("Failed to reapply route: {}", routeCommand);
                }

            } catch (Exception e) {
                logger.warn("Failed to reapply route", e);
            }
        }

        logger.info("Completed reapplying routes for interface: {}", targetInterface);
    }

    /**
     * 执行单个路由命令
     * @param routeCommand 路由命令
     * @return 是否执行成功
     */
    private boolean executeRouteCommand(String routeCommand) {
        try {
            logger.debug("Executing route command: {}", routeCommand);
            ProcessBuilder pb = new ProcessBuilder("bash", "-c", routeCommand);
            Process process = pb.start();
            int exitCode = process.waitFor();

            if (exitCode == 0) {
                logger.debug("Route command executed successfully: {}", routeCommand);
                return true;
            } else {
                logger.warn("Route command failed with exit code {}: {}", exitCode, routeCommand);
                return false;
            }
        } catch (Exception e) {
            logger.error("Error executing route command: {}", routeCommand, e);
            return false;
        }
    }

    /**
     * 将成功的路由写入配置文件
     * @param successfulRoutes 成功的路由列表
     * @param targetInterface 目标网卡名称
     * @throws IOException 文件操作异常
     */
    private void updateRouteConfigFile(List<RouteList> successfulRoutes, String targetInterface) throws IOException {
        Path scriptPath = Paths.get(ROUTE_SCRIPT_PATH);
        List<String> lines = new ArrayList<>();

        // 如果文件存在，读取现有内容
        if (Files.exists(scriptPath)) {
            lines = new ArrayList<>(Files.readAllLines(scriptPath));
        } else {
            // 添加文件头注释
            lines.add("#!/bin/bash");
            lines.add("# Route configuration file - Auto-generated, do not edit manually");
        }

        // 添加成功的路由到配置文件
        for (RouteList route : successfulRoutes) {
            try {
                String routeCommand = buildRouteCommand(route, targetInterface);
                lines.add(routeCommand);
                logger.debug("Added route to config file: {}", routeCommand);
            } catch (Exception e) {
                logger.warn("Failed to build route command for config file", e);
            }
        }

        // 写入文件
        Files.write(scriptPath, lines);

        // 设置脚本文件为可执行
        try {
            ProcessBuilder pb = new ProcessBuilder("chmod", "+x", ROUTE_SCRIPT_PATH);
            Process process = pb.start();
            process.waitFor();
        } catch (Exception e) {
            logger.warn("Failed to set execute permission for route script", e);
        }

        logger.info("Updated route config file with {} routes for interface: {}",
                   successfulRoutes.size(), targetInterface);
    }

    /**
     * 从路由命令中提取网络部分（用于删除操作）
     * @param routeCommand 完整的路由命令
     * @return 网络部分（如：***********/24）
     */
    private String extractNetworkPart(String routeCommand) {
        try {
            // 路由命令格式：ip route add ***********/24 via ******** dev eth0
            String[] parts = routeCommand.split("\\s+");

            if (parts.length >= 4 && "ip".equals(parts[0]) && "route".equals(parts[1]) && "add".equals(parts[2])) {
                return parts[3]; // 返回网络部分，如：***********/24
            } else {
                logger.warn("Invalid route command format: {}", routeCommand);
                return "";
            }
        } catch (Exception e) {
            logger.warn("Failed to extract network part from route command: {}", routeCommand, e);
            return "";
        }
    }

    /**
     * 构建路由命令
     * @param route 路由对象
     * @param targetInterface 目标网卡名称
     * @return 路由命令字符串
     * @throws Exception 构建异常
     */
    private String buildRouteCommand(RouteList route, String targetInterface) throws Exception {
        String networkAddress = IPUtil.ipAddressToString(route.getDestination());
        String subnetMask = IPUtil.ipAddressToString(route.getSubnetMask());
        String gateway = IPUtil.ipAddressToString(route.getGateway());
        int metric =(int) route.getMetric();

        // 将子网掩码转换为CIDR格式
        String cidrNetwork = convertToCIDR(networkAddress, subnetMask);

        // 构建路由命令：ip route add NETWORK/MASK via GATEWAY dev INTERFACE metric METRIC table TABLE
        StringBuilder command = new StringBuilder();
        command.append("ip route add ").append(cidrNetwork)
               .append(" via ").append(gateway)
               .append(" dev ").append(targetInterface);

        // 添加metric参数（如果不为0）
        if (metric > 0) {
            command.append(" metric ").append(metric);
        }

        // 添加table参数：eth0使用manager表，eth1使用business表
        String tableName = getTableName(targetInterface);
        command.append(" table ").append(tableName);

        return command.toString();
    }
    
    /**
     * 将IP地址和子网掩码转换为CIDR格式
     * @param networkAddress 网络地址
     * @param subnetMask 子网掩码
     * @return CIDR格式的网络地址
     */
    private String convertToCIDR(String networkAddress, String subnetMask) {
        try {
            // 计算CIDR前缀长度
            String[] maskOctets = subnetMask.split("\\.");
            if (maskOctets.length != 4) {
                throw new IllegalArgumentException("Invalid subnet mask format: " + subnetMask);
            }
            
            int prefixLength = 0;
            for (String octet : maskOctets) {
                int value = Integer.parseInt(octet);
                prefixLength += Integer.bitCount(value);
            }
            
            return networkAddress + "/" + prefixLength;
            
        } catch (Exception e) {
            logger.warn("Failed to convert to CIDR format: {} {}, using /24 as default", networkAddress, subnetMask);
            return networkAddress + "/24";
        }
    }
    

    
    /**
     * 根据网卡类型获取网卡名称
     * @param interfaceType 网卡类型
     * @return 网卡名称
     */
    private String getInterfaceName(InterfaceType interfaceType) {
        if (interfaceType == InterfaceType.management) {
            return "eth0";
        } else if (interfaceType == InterfaceType.business) {
            return "eth1";
        } else {
            logger.warn("Unknown interface type: {}, defaulting to eth0", interfaceType);
            return "eth0";
        }
    }

    /**
     * 根据网卡名称获取对应的路由表名
     * @param interfaceName 网卡名称
     * @return 路由表名
     */
    private String getTableName(String interfaceName) {
        if ("eth0".equals(interfaceName)) {
            return "manager";
        } else if ("eth1".equals(interfaceName)) {
            return "business";
        } else {
            logger.warn("Unknown interface name: {}, defaulting to manager table", interfaceName);
            return "manager";
        }
    }
}
