package com.unimas.asn.logic;

import com.oss.asn1.INTEGER;
import com.unimas.asn.servicemanager.servicemanagementhttp.*;
import com.unimas.asn.util.IPUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 查询当前路由逻辑处理类
 * 负责使用 ip route show table all 命令查询当前设备生效的路由信息
 * 相比 route -n 命令，能够获取更完整的路由表信息，包括所有自定义路由表
 */
public class QueryCurRouteLogic {
    private static final Logger logger = LoggerFactory.getLogger(QueryCurRouteLogic.class);
    
    /**
     * 查询当前路由
     * @param request 查询当前路由请求
     * @return 查询当前路由响应
     */
    public QueryCurRouteResponse queryCurRoute(QueryCurRouteRequest request) {
        logger.info("Processing QueryCurRouteRequest for interface: {}", request.getInterfaceType());
        
        // 创建响应对象
        QueryCurRouteResponse response = new QueryCurRouteResponse();
        response.setMessageType(ContentMessageType.iprouteService);
        response.setInterfaceType(request.getInterfaceType());
        
        try {
            // 执行route -n命令获取当前路由表
            List<RouteList> routes = getCurrentRoutes(request.getInterfaceType());
            
            // 创建路由列表
            QueryCurRouteResponse.RouteList routeList = new QueryCurRouteResponse.RouteList();
            for (RouteList route : routes) {
                routeList.add(route);
            }
            
            response.setRouteList(routeList);
            response.setResult(0); // 成功
            
            logger.info("Successfully queried current routes for interface: {}, found {} routes", 
                       request.getInterfaceType(), routes.size());
            
        } catch (Exception e) {
            logger.error("Failed to query current routes for interface: {}", request.getInterfaceType(), e);
            
            // 创建空的路由列表
            QueryCurRouteResponse.RouteList emptyRouteList = new QueryCurRouteResponse.RouteList();
            response.setRouteList(emptyRouteList);
            response.setResult(1); // 失败
        }
        
        return response;
    }
    
    /**
     * 执行ip route show table all命令获取当前路由表，并过滤指定网卡的路由
     * @param interfaceType 网卡类型
     * @return 路由列表
     * @throws IOException 命令执行异常
     */
    private List<RouteList> getCurrentRoutes(InterfaceType interfaceType) throws IOException {
        List<RouteList> routes = new ArrayList<>();
        
        // 确定目标网卡名称
        String targetInterface = getInterfaceName(interfaceType);
        
        try {
            // 执行ip route show table all命令
            ProcessBuilder pb = new ProcessBuilder("ip", "route", "show", "table", "all");
            Process process = pb.start();
            
            // 读取命令输出
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                
                // 跳过空行
                if (line.isEmpty()) {
                    continue;
                }
                
                // 解析路由行
                RouteList route = parseIpRouteLine(line, targetInterface);
                if (route != null) {
                    routes.add(route);
                }
            }
            
            // 等待命令完成
            int exitCode = process.waitFor();
            if (exitCode != 0) {
                logger.warn("ip route show table all command exited with code: {}", exitCode);
            }
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new IOException("Command execution interrupted", e);
        }
        
        logger.info("Parsed {} current routes for interface: {}", routes.size(), targetInterface);
        
        return routes;
    }
    
    /**
     * 解析ip route命令输出的单行路由信息
     * 格式示例：
     * - default via *********** dev eth0 table main
     * - ***********/24 dev eth0 proto kernel scope link src ***********00 table main
     * - ***********/24 dev eth1 table business
     * @param line 路由行
     * @param targetInterface 目标网卡名称
     * @return 解析后的路由对象，如果不匹配则返回null
     */
    private RouteList parseIpRouteLine(String line, String targetInterface) {
        try {
            // 过滤掉不需要的路由类型
            if (shouldSkipRoute(line)) {
                return null;
            }
            
            // 检查是否包含目标网卡
            if (!line.contains("dev " + targetInterface)) {
                logger.debug("Interface {} not found in line: {}", targetInterface, line);
                return null;
            }
            
            // 跳过表头信息行
            if (line.startsWith("table ")) {
                return null;
            }
            
            RouteList route = new RouteList();
            
            // 解析目标网络
            String destination = "0.0.0.0";
            String netmask = "0.0.0.0";
            
            if (line.startsWith("default")) {
                // 默认路由
                destination = "0.0.0.0";
                netmask = "0.0.0.0";
            } else {
                // 网络路由，提取目标网络
                String[] parts = line.split("\\s+");
                if (parts.length > 0) {
                    String networkPart = parts[0];
                    if (networkPart.contains("/")) {
                        // CIDR格式：***********/24
                        String[] cidrParts = networkPart.split("/");
                        destination = cidrParts[0];
                        int prefixLength = Integer.parseInt(cidrParts[1]);
                        netmask = cidrToNetmask(prefixLength);
                    } else {
                        // 单个IP地址
                        destination = networkPart;
                        netmask = "***************";
                    }
                }
            }
            
            // 解析网关
            String gateway = "0.0.0.0";
            Pattern viaPattern = Pattern.compile("via\\s+(\\S+)");
            Matcher viaMatcher = viaPattern.matcher(line);
            if (viaMatcher.find()) {
                gateway = viaMatcher.group(1);
            }
            
            // 解析metric
            int metric = 0;
            Pattern metricPattern = Pattern.compile("metric\\s+(\\d+)");
            Matcher metricMatcher = metricPattern.matcher(line);
            if (metricMatcher.find()) {
                metric = Integer.parseInt(metricMatcher.group(1));
            }
            
            // 设置路由对象属性
            route.setDestination(IPUtil.createIPAddress(destination));
            route.setSubnetMask(IPUtil.createIPAddress(netmask));
            route.setGateway(IPUtil.createIPAddress(gateway));
            route.setMetric(new INTEGER(metric));
            
            logger.debug("Parsed ip route: dest={}, mask={}, gateway={}, metric={}, interface={}", 
                        destination, netmask, gateway, metric, targetInterface);
            
            return route;
            
        } catch (Exception e) {
            logger.warn("Failed to parse ip route line: {}", line, e);
            return null;
        }
    }
    
    /**
     * 判断是否应该跳过某个路由条目
     * @param line 路由行
     * @return true表示应该跳过，false表示需要处理
     */
    private boolean shouldSkipRoute(String line) {
        // 跳过IPv6路由（包含冒号）
        if (line.contains(":")) {
            return true;
        }
        
        // 跳过本地路由
        if (line.startsWith("local ")) {
            return true;
        }
        
        // 跳过广播路由
        if (line.startsWith("broadcast ")) {
            return true;
        }
        
        // 跳过组播路由
        if (line.startsWith("multicast ")) {
            return true;
        }
        
        // 跳过包含table local的路由
        if (line.contains("table local")) {
            return true;
        }
        
        // 跳过空行
        if (line.trim().isEmpty()) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 将CIDR前缀长度转换为子网掩码
     * @param prefixLength CIDR前缀长度
     * @return 子网掩码字符串
     */
    private String cidrToNetmask(int prefixLength) {
        if (prefixLength < 0 || prefixLength > 32) {
            return "***************";
        }
        
        int mask = 0xFFFFFFFF << (32 - prefixLength);
        return String.format("%d.%d.%d.%d",
            (mask >>> 24) & 0xFF,
            (mask >>> 16) & 0xFF,
            (mask >>> 8) & 0xFF,
            mask & 0xFF);
    }
    /**
     * 根据网卡类型获取网卡名称
     * @param interfaceType 网卡类型
     * @return 网卡名称
     */
    private String getInterfaceName(InterfaceType interfaceType) {
        if (interfaceType == InterfaceType.management) {
            return "eth0";
        } else if (interfaceType == InterfaceType.business) {
            return "eth1";
        } else {
            logger.warn("Unknown interface type: {}, defaulting to eth0", interfaceType);
            return "eth0";
        }
    }
}
