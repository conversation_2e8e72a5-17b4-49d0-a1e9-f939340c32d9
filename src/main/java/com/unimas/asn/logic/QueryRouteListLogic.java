package com.unimas.asn.logic;

import com.oss.asn1.INTEGER;
import com.unimas.asn.servicemanager.servicemanagementhttp.*;
import com.unimas.asn.util.IPUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 查询路由列表逻辑处理类
 * 负责读取和解析 /etc/unimas/tomcat/conf/route.sh 文件中的路由配置
 */
public class QueryRouteListLogic {
    private static final Logger logger = LoggerFactory.getLogger(QueryRouteListLogic.class);
    private static final String ROUTE_SCRIPT_PATH = "/etc/unimas/tomcat/conf/route.sh";
    
    /**
     * 查询路由列表
     * @param request 查询路由列表请求
     * @return 查询路由列表响应
     */
    public QueryRouteListResponse queryRouteList(QueryRouteListRequest request) {
        logger.info("Processing QueryRouteListRequest for interface: {}", request.getInterfaceType());
        
        // 创建响应对象
        QueryRouteListResponse response = new QueryRouteListResponse();
        response.setMessageType(ContentMessageType.iprouteService);
        response.setInterfaceType(request.getInterfaceType());
        
        try {
            // 读取路由配置文件
            List<RouteList> routes = parseRouteScript(request.getInterfaceType());
            
            // 创建路由列表
            QueryRouteListResponse.RouteList routeList = new QueryRouteListResponse.RouteList();
            for (RouteList route : routes) {
                routeList.add(route);
            }
            
            response.setRouteList(routeList);
            response.setResult(0); // 成功
            
            logger.info("Successfully queried route list for interface: {}, found {} routes", 
                       request.getInterfaceType(), routes.size());
            
        } catch (Exception e) {
            logger.error("Failed to query route list for interface: {}", request.getInterfaceType(), e);
            
            // 创建空的路由列表
            QueryRouteListResponse.RouteList emptyRouteList = new QueryRouteListResponse.RouteList();
            response.setRouteList(emptyRouteList);
            response.setResult(1); // 失败
        }
        
        return response;
    }
    
    /**
     * 解析路由脚本文件，提取指定网卡的路由信息
     * @param interfaceType 网卡类型
     * @return 路由列表
     * @throws IOException 文件读取异常
     */
    private List<RouteList> parseRouteScript(InterfaceType interfaceType) throws IOException {
        List<RouteList> routes = new ArrayList<>();
        
        Path scriptPath = Paths.get(ROUTE_SCRIPT_PATH);
        if (!Files.exists(scriptPath)) {
            logger.warn("Route script file does not exist: {}", ROUTE_SCRIPT_PATH);
            return routes;
        }
        
        // 确定目标网卡名称
        String targetInterface = getInterfaceName(interfaceType);
        
        // 读取文件内容
        List<String> lines = Files.readAllLines(scriptPath);
        
        // 解析每一行
        for (String line : lines) {
            line = line.trim();
            
            // 跳过注释和空行
            if (line.isEmpty() || line.startsWith("#")) {
                continue;
            }
            
            // 解析路由命令：ip route add **********/24 via ********* dev eth0
            RouteList route = parseRouteLine(line, targetInterface);
            if (route != null) {
                routes.add(route);
            }
        }
        
        logger.info("Parsed {} routes for interface: {} from script: {}", 
                   routes.size(), targetInterface, ROUTE_SCRIPT_PATH);
        
        return routes;
    }
    
    /**
     * 解析单行路由命令
     * @param line 路由命令行
     * @param targetInterface 目标网卡名称
     * @return 解析后的路由对象，如果不匹配则返回null
     */
    private RouteList parseRouteLine(String line, String targetInterface) {
        try {
            // 匹配路由命令格式：ip route add NETWORK/MASK via GATEWAY dev INTERFACE [metric METRIC]
            Pattern pattern = Pattern.compile("ip\\s+route\\s+add\\s+(\\S+)\\s+via\\s+(\\S+)\\s+dev\\s+(\\S+)(?:\\s+metric\\s+(\\d+))?");
            Matcher matcher = pattern.matcher(line);

            if (!matcher.find()) {
                logger.debug("Line does not match route pattern: {}", line);
                return null;
            }

            String networkCidr = matcher.group(1);
            String gateway = matcher.group(2);
            String interfaceName = matcher.group(3);
            String metricStr = matcher.group(4); // 可能为null

            // 检查是否为目标网卡
            if (!targetInterface.equals(interfaceName)) {
                logger.debug("Interface {} does not match target {}, skipping", interfaceName, targetInterface);
                return null;
            }

            // 解析网络地址和子网掩码
            String[] networkParts = networkCidr.split("/");
            if (networkParts.length != 2) {
                logger.warn("Invalid CIDR format: {}", networkCidr);
                return null;
            }

            String networkAddress = networkParts[0];
            int prefixLength = Integer.parseInt(networkParts[1]);
            String subnetMask = cidrToSubnetMask(prefixLength);

            // 解析metric值
            int metric = 0; // 默认值
            if (metricStr != null && !metricStr.isEmpty()) {
                try {
                    metric = Integer.parseInt(metricStr);
                } catch (NumberFormatException e) {
                    logger.warn("Invalid metric value: {}, using default 0", metricStr);
                }
            }

            // 创建路由对象
            RouteList route = new RouteList();
            route.setDestination(IPUtil.createIPAddress(networkAddress));
            route.setSubnetMask(IPUtil.createIPAddress(subnetMask));
            route.setGateway(IPUtil.createIPAddress(gateway));
            route.setMetric(new INTEGER(metric));

            logger.debug("Parsed route: network={}, mask={}, gateway={}, metric={}, interface={}",
                        networkAddress, subnetMask, gateway, metric, interfaceName);

            return route;
            
        } catch (Exception e) {
            logger.warn("Failed to parse route line: {}", line, e);
            return null;
        }
    }
    
    /**
     * 根据网卡类型获取网卡名称
     * @param interfaceType 网卡类型
     * @return 网卡名称
     */
    private String getInterfaceName(InterfaceType interfaceType) {
        if (interfaceType == InterfaceType.management) {
            return "eth0";
        } else if (interfaceType == InterfaceType.business) {
            return "eth1";
        } else {
            logger.warn("Unknown interface type: {}, defaulting to eth0", interfaceType);
            return "eth0";
        }
    }
    
    /**
     * 将CIDR前缀长度转换为子网掩码
     * @param prefixLength CIDR前缀长度
     * @return 子网掩码字符串
     */
    private String cidrToSubnetMask(int prefixLength) {
        if (prefixLength < 0 || prefixLength > 32) {
            throw new IllegalArgumentException("Invalid prefix length: " + prefixLength);
        }
        
        int mask = 0xFFFFFFFF << (32 - prefixLength);
        
        int octet1 = (mask >>> 24) & 0xFF;
        int octet2 = (mask >>> 16) & 0xFF;
        int octet3 = (mask >>> 8) & 0xFF;
        int octet4 = mask & 0xFF;
        
        return octet1 + "." + octet2 + "." + octet3 + "." + octet4;
    }
}
