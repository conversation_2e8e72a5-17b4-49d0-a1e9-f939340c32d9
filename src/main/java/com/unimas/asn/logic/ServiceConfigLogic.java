package com.unimas.asn.logic;

import com.unimas.asn.bean.UdpConfig;

import com.unimas.asn.http.ValidationException;
import com.unimas.asn.servicemanager.servicemanagementhttp.*;
import com.unimas.asn.util.ConfigXmlOperator;
import com.unimas.asn.util.Constant;
import com.unimas.asn.util.IPUtil;
import com.unimas.asn.util.ServiceXmlOperator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


import java.io.File;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.NetworkInterface;
import java.net.InetAddress;
import java.util.Enumeration;

import java.util.HashMap;

import java.util.Map;

public class ServiceConfigLogic {

//    private static final String SERVICE_PRE_PATH = "";
//    private static final String SERVICE_FILE_EXT = ".xml";
    private static final Logger log = LoggerFactory.getLogger(ServiceConfigLogic.class);

    public ServiceConfigResponse serviceConfigRequest(ServiceConfigRequest request) throws ValidationException {
        ServiceConfigResponse ret = null;
        if(request.getAddServiceRequest() != null) {
            ret = addService(request.getAddServiceRequest());
        }else if(request.getUpdateServiceRequest() != null) {
            ret = updateService(request.getUpdateServiceRequest());
        }else if(request.getDeleteServiceRequest() != null) {
            ret = deleteService(request.getDeleteServiceRequest());
        }

        return ret;
    }
    private ServiceConfigResponse addService(AddServiceRequest request) throws ValidationException {
        ServiceConfigResponse ret = null;
        if(request != null){
            String serviceName = new String(request.getDisplayname().byteArrayValue()).trim();
            if(!isAllZerosForSmallArray(request.getDisplayname().byteArrayValue()) && !serviceName.isEmpty() && !containsInvalidXMLChars(serviceName)){
                ConfigXmlOperator configXmlOperator = new ConfigXmlOperator(Constant.CONFIG_PATH);
                if(!configXmlOperator.isDisplayNameExist(serviceName)) {
                    // 创建UdpConfig对象
                    UdpConfig udpConfig = new UdpConfig();
                    udpConfig.setDisplayname(serviceName);
                    
                    // 根据网络类型设置不同参数
                    Network network = request.getNetwork();
                    if (network == Network.sender) {
                        // 发送端特有字段
                        String proxyIp = IPUtil.ipAddressToString(request.getProxyIp());

                        // 如果proxyIp为null，使用eth1的IP地址作为代理IP
                        if (proxyIp == null) {
                            proxyIp = getEth1IPAddress();
                            if (proxyIp != null) {
                                log.info("ProxyIp was null, using eth1 IP address as proxy: {}", proxyIp);
                            } else {
                                log.warn("ProxyIp was null and failed to get eth1 IP address, proxy IP will be null");
                                throw new ValidationException("ProxyIp was null and failed to get eth1 IP address",ProcessErrorState.illegalArgumentError,ContentMessageType.addService);
                            }
                        }

                        Integer proxyPort = request.getProxyPort().intValue();
                        udpConfig.setIpproxy(proxyIp);
                        udpConfig.setPortclient(proxyPort != null ? proxyPort.toString() : null);
                        
                        // 处理ContentKeyCheck位图
                        if (request.getContentKeyCheck() != null) {
                            byte[] contentKeyCheck = request.getContentKeyCheck().byteArrayValue();
                            if (contentKeyCheck.length > 0) {
                                // 检查第0位 - 机动车牌校验
                                udpConfig.setCarcheck(((contentKeyCheck[0] & 0x01) != 0) ? "1" : "0");
                                // 检查第1位 - 身份证校验
                                udpConfig.setIdcheck(((contentKeyCheck[0] & 0x02) != 0) ? "1" : "0");
                            }
                        } else {
                            // 默认值
                            udpConfig.setCarcheck("0");
                            udpConfig.setIdcheck("0");
                        }
                        
                        // 处理ProtocolFilter位图
                        if (request.getProtocolFilter() != null) {
                            byte[] protocolFilter = request.getProtocolFilter().byteArrayValue();
                            if (protocolFilter.length > 0) {
                                // 检查第0位 - CRC16格式过滤
                                udpConfig.setCrcfilter(((protocolFilter[0] & 0x01) != 0) ? "1" : "0");
                                // 检查第1位 - ASN格式过滤
                                udpConfig.setAsnfilter(((protocolFilter[0] & 0x02) != 0) ? "1" : "0");
                            }
                        } else {
                            // 默认值
                            udpConfig.setCrcfilter("0");
                            udpConfig.setAsnfilter("0");
                        }
                        
                        // 处理UnpassDeal枚举
                        if (request.getUnpassDeal() != null) {
                            if(request.getUnpassDeal() == PermissionState.allow){
                                udpConfig.setUnpassdeal("0"); // 仅报警数据正常传输
                            }else if(request.getUnpassDeal() == PermissionState.forbidden){
                                udpConfig.setUnpassdeal("1"); // 报警+数据丢弃不转发
                            }else{
                                udpConfig.setUnpassdeal("0");
                            }
                        } else {
                            udpConfig.setUnpassdeal("0");
                        }
                        
                    } else if (network == Network.receiver) {
                        // 接收端特有字段
                        String serverIp = IPUtil.ipAddressToString(request.getServerIp());
                        Integer serverPort = request.getServerPort().intValue();
                        udpConfig.setHostip(serverIp);
                        udpConfig.setHostport(serverPort != null ? serverPort.toString() : null);
                    }
                    
                    // 设置其他默认值
//                    udpConfig.setDestResLabel("proxyclient_2");
//                    udpConfig.setSendaddrmap("");
                    
                    //保存config.xml
                    int serviceId = configXmlOperator.add(builderConfigNode(udpConfig));
                    udpConfig.setSid(serviceId + "");
                    //保存service.xml,先删除可能遗留的
                    File file = new File(Constant.SERVICE_PRE_PATH+serviceId+Constant.SERVICE_FILE_EXT);
                    if(file.exists())file.delete();
                    ServiceXmlOperator serviceXmlOperator = new ServiceXmlOperator(serviceId+"");
                    serviceXmlOperator.saveUdpConfig(udpConfig);
//                    serviceXmlOperator.setAttributes(builderServiceConfigNode(udpConfig));
                    //保存properties
                    serviceXmlOperator.generateProperties(Constant.SERVICE_PRE_PATH+serviceId+".properties",udpConfig);
                    ServiceConfigResponse response = new ServiceConfigResponse();
                    response.setMessageType(ContentMessageType.addService);
                    response.setServiceId(new ServiceId(serviceId));
                    ret = response;
                }else{ //服务名重复
                    log.error("service name is conflict ... ");
//                    throw new Exception("service name is conflict ... ");
                      throw new ValidationException("service name is conflict ... ", ProcessErrorState.displayNameConflictError,ContentMessageType.addService);
//                    ret = new ErrorResponse(ContentMessageType.addService, ProcessErrorState.displayNameConflictError);
                }
            }else{
                log.error("service name is invalid ... ");
                throw new ValidationException("service name is conflict ... ", ProcessErrorState.illegalArgumentError,ContentMessageType.addService);
            }
        }else{

        }
        return ret;
    }
    boolean isAllZerosForSmallArray(byte[] bytes) {
        long value = 0;
        for (byte b : bytes) {
            value |= b;
        }
        return value == 0;
    }
    public static boolean containsInvalidXMLChars(String value) {
        // XML 1.0规范不允许的字符范围
        return value.matches(".*[^\\x09\\x0A\\x0D\\x20-\\xD7FF\\xE000-\\xFFFD\\x10000-x10FFFF].*");
    }
    private Map<String,String> builderConfigNode(UdpConfig udpConfig){
        Map<String,String> map = new HashMap<>();
        map.put("creator","unimas");
        map.put("isAudit","true");
        map.put("templateid","");
        map.put("type","11");
        map.put("importServiceId","");
        map.put("servicetype","udp");
        map.put("configedtime","1");
        map.put("displayname",udpConfig.getDisplayname());
        map.put("secstate","1");
        map.put("isRun","false");
        map.put("istemplate","false");
        map.put("seclevel","4");
        map.put("flowlevel","10");
        map.put("status","deployed");
        map.put("syncStatus","CONFIG_COMPLETED");
        return map;
    }
    private Map<String,String>builderServiceConfigNode(UdpConfig udpConfig){
        Map<String,String> map = new HashMap<>();
        map.put("level","");
        map.put("eqs","");
        map.put("weekday","1;2;3;4;5;6;7");
        map.put("special_value","");
        map.put("runtime","#*#");
        map.put("rules","");
        map.put("ipproxy",udpConfig.getIpproxy());
        map.put("multicast","false");
        map.put("srcResLabel","proxyclient_1");
        map.put("configedtime","1");
        map.put("portclient",udpConfig.getPortclient());
        map.put("audit","1");
        map.put("displayname",udpConfig.getDisplayname());
        map.put("iprange","#*#");
        map.put("udpFlood","0");
        map.put("istemplate","false");
        map.put("proxymode","udp");
        map.put("multicastip","");
        map.put("flowlevel","10");
        
        // 添加5个新属性
        if (udpConfig.getHostip() != null) {
            map.put("hostip", udpConfig.getHostip());
        }
        if (udpConfig.getDestResLabel() != null) {
            map.put("destResLabel", udpConfig.getDestResLabel());
        } else {
            map.put("destResLabel", "proxyclient_2"); // 默认值
        }
        if (udpConfig.getSendaddrmap() != null) {
            map.put("sendaddrmap", udpConfig.getSendaddrmap());
        } else {
            map.put("sendaddrmap", "");
        }
        if (udpConfig.getHostport() != null) {
            map.put("hostport", udpConfig.getHostport());
        }
        if (udpConfig.getSrcPort() != null) {
            map.put("srcPort", udpConfig.getSrcPort());
        }
        
        // 检测和过滤相关属性
        if (udpConfig.getCarcheck() != null) {
            map.put("carcheck", udpConfig.getCarcheck());
        } else {
            map.put("carcheck", "0"); // 默认关闭
        }
        if (udpConfig.getIdcheck() != null) {
            map.put("idcheck", udpConfig.getIdcheck());
        } else {
            map.put("idcheck", "0"); // 默认关闭
        }
        if (udpConfig.getCrcfilter() != null) {
            map.put("crcfilter", udpConfig.getCrcfilter());
        } else {
            map.put("crcfilter", "0"); // 默认关闭
        }
        if (udpConfig.getAsnfilter() != null) {
            map.put("asnfilter", udpConfig.getAsnfilter());
        } else {
            map.put("asnfilter", "0"); // 默认关闭
        }
        if (udpConfig.getUnpassdeal() != null) {
            map.put("unpassdeal", udpConfig.getUnpassdeal());
        } else {
            map.put("unpassdeal", "0"); // 默认仅报警数据正常传输
        }
        
        return map;
    }
    private ServiceConfigResponse deleteService(DeleteServiceRequest request) throws ValidationException {
        ServiceConfigResponse ret = null;
        if(request != null){
            int serviceId = request.getServiceId().intValue();
            ServiceStatusLogic serviceStatusLogic = new ServiceStatusLogic();
            boolean b = serviceStatusLogic.stopService(serviceId + "");
            if(b){
                log.info("stop service " + serviceId + " " + b);
                //删除properties文件
                File propertiesFile = new File(Constant.SERVICE_PRE_PATH+serviceId+".properties");
                boolean delRet = propertiesFile.delete();
                log.info("properties file deleted : "+delRet);
                //删除service.xml
                File serciceConfigFile = new File(Constant.SERVICE_PRE_PATH+serviceId+Constant.SERVICE_FILE_EXT);
                delRet = serciceConfigFile.delete();
                log.info("serciceConfig file deleted : "+delRet);
                //删除config.xml节点
                ConfigXmlOperator configXmlOperator = new ConfigXmlOperator(Constant.CONFIG_PATH);
                configXmlOperator.deleteNode(serviceId+"");


                ret = new ServiceConfigResponse();
                ret.setServiceId(new ServiceId(serviceId));
                ret.setMessageType(ContentMessageType.deleteService);
            }else{
                log.info("delete service " + serviceId + " failed");
                throw new ValidationException("服务无法停止",ProcessErrorState.illegalOperationError,ContentMessageType.deleteService);
            }
        }
        return ret;
    }
    private ServiceConfigResponse updateService(UpdateServiceRequest request) throws ValidationException {
        ServiceConfigResponse ret = null;
        if(request != null) {
            int serviceId = request.getServiceId().intValue();
            Network network = request.getNetwork();

            try {
                //判断启停，停止才能修改
                ConfigXmlOperator configXmlOperator = new ConfigXmlOperator(Constant.CONFIG_PATH);
                if(configXmlOperator.getIsRun(serviceId + "")){
                    log.info("service " + serviceId + " is running");
                    throw  new Exception(ProcessErrorState.illegalOperationError.toString());
//                    return new ErrorResponse(request.getMessageType(),ProcessErrorState.IllegalOperationError);
                }
                // 读取现有配置
                ServiceXmlOperator serviceXmlOperator = new ServiceXmlOperator(serviceId+"");
                UdpConfig udpConfig = serviceXmlOperator.loadUdpConfig();
                
                // 更新udpConfig对象的属性
                if (network == Network.sender) {
                    // 发送端特有字段
                    if (request.getProxyIp() != null) {
                        String proxyIp = IPUtil.ipAddressToString(request.getProxyIp());

                        // 如果proxyIp为null，使用eth1的IP地址作为代理IP
                        if (proxyIp == null) {
                            proxyIp = getEth1IPAddress();
                            if (proxyIp != null) {
                                log.info("ProxyIp was null in update request, using eth1 IP address as proxy: {}", proxyIp);
                            } else {
                                log.warn("ProxyIp was null in update request and failed to get eth1 IP address, proxy IP will be null");
                                throw new Exception("ProxyIp was null in update request and failed to get eth1 IP address");
                            }
                        }

                        udpConfig.setIpproxy(proxyIp);
                    }
                    if (request.getProxyPort() != null) {
                        udpConfig.setPortclient(request.getProxyPort().intValue()+"");
                    }
                    
                    // 更新检测和过滤相关属性
                    if (request.getContentKeyCheck() != null) {
                        // 从ContentKeyCheck位图中提取值
                        byte[] contentKeyCheck = request.getContentKeyCheck().byteArrayValue();
                        if (contentKeyCheck.length > 0) {
                            // 检查第0位 - 机动车牌校验
                            udpConfig.setCarcheck(((contentKeyCheck[0] & 0x01) != 0) ? "1" : "0");
                            // 检查第1位 - 身份证校验
                            udpConfig.setIdcheck(((contentKeyCheck[0] & 0x02) != 0) ? "1" : "0");
                        }
                    }
                    
                    if (request.getProtocolFilter() != null) {
                        // 从ProtocolFilter位图中提取值
                        byte[] protocolFilter = request.getProtocolFilter().byteArrayValue();
                        if (protocolFilter.length > 0) {
                            // 检查第0位 - CRC16格式过滤
                            udpConfig.setCrcfilter(((protocolFilter[0] & 0x01) != 0) ? "1" : "0");
                            // 检查第1位 - ASN格式过滤
                            udpConfig.setAsnfilter(((protocolFilter[0] & 0x02) != 0) ? "1" : "0");
                        }
                    }

                    if (request.getUnpassDeal() != null) {
                        if(request.getUnpassDeal() == PermissionState.allow){
                            udpConfig.setUnpassdeal("0"); // 仅报警数据正常传输
                        }else if(request.getUnpassDeal() == PermissionState.forbidden){
                            udpConfig.setUnpassdeal("1"); // 报警+数据丢弃不转发
                        }else{
                            udpConfig.setUnpassdeal("0");
                        }
                    } else {
                        udpConfig.setUnpassdeal("0");
                    }
                    
                } else if (network == Network.receiver) {
                    // 接收端特有字段
                    if (request.getServerIp() != null) {
                        udpConfig.setHostip(IPUtil.ipAddressToString(request.getServerIp()));
                    }
                    if (request.getServerPort() != null) {
                        udpConfig.setHostport(request.getServerPort().intValue()+"");
                    }
                }
                
                // 保存服务配置
                Map<String, String> configAttributes = builderServiceConfigNode(udpConfig);
                serviceXmlOperator.setAttributes(configAttributes);
                
                // 更新properties文件
                serviceXmlOperator.generateProperties(Constant.SERVICE_PRE_PATH+serviceId+".properties", udpConfig);

                // 创建响应
                ret = new ServiceConfigResponse();
                ret.setServiceId(new ServiceId(serviceId));
                ret.setMessageType(ContentMessageType.updateService);
                
            } catch (Exception e) {
                log.error("更新服务配置失败", e);
//                return new ErrorResponse(CommonTypes.ContentMessageType.UPDATE_SERVICE, ProcessErrorState.IllegalArgumentError);
//                throw new Exception(ProcessErrorState.illegalArgumentError.toString());
                throw new ValidationException("更新服务配置失败",ProcessErrorState.illegalArgumentError,ContentMessageType.updateService);
            }
        } else {
            log.error("更新服务请求为空");
            throw new ValidationException("更新服务请求为空", ProcessErrorState.messageStructureError,ContentMessageType.updateService);
        }
        
        return ret;
    }

    /**
     * 获取eth1网卡的IP地址
     * @return eth1的IP地址字符串，如果获取失败返回null
     */
    private String getEth1IPAddress() {
        try {
            // 方法1：使用NetworkInterface API获取eth1的IP地址
            NetworkInterface networkInterface = NetworkInterface.getByName("eth1");
            if (networkInterface != null) {
                Enumeration<InetAddress> addresses = networkInterface.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    InetAddress addr = addresses.nextElement();
                    if (!addr.isLoopbackAddress() && addr.getAddress().length == 4) {
                        String ipAddress = addr.getHostAddress();
                        log.info("Successfully obtained eth1 IP address using NetworkInterface API: {}", ipAddress);
                        return ipAddress;
                    }
                }
            }

            // 方法2：如果NetworkInterface API失败，使用ip命令获取eth1的IP地址
            log.warn("Failed to get eth1 IP using NetworkInterface API, trying ip command");
            ProcessBuilder pb = new ProcessBuilder("ip", "addr", "show", "eth1");
            Process process = pb.start();

            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    if (line.contains("inet ") && !line.contains("inet6")) {
                        // 解析IP地址，格式如：inet *************/24 brd ************* scope global eth1
                        String[] parts = line.trim().split("\\s+");
                        for (int i = 0; i < parts.length; i++) {
                            if ("inet".equals(parts[i]) && i + 1 < parts.length) {
                                String ipWithPrefix = parts[i + 1];
                                if (ipWithPrefix.contains("/")) {
                                    String ipAddress = ipWithPrefix.split("/")[0];
                                    log.info("Successfully obtained eth1 IP address using ip command: {}", ipAddress);
                                    return ipAddress;
                                }
                                log.info("Successfully obtained eth1 IP address using ip command: {}", ipWithPrefix);
                                return ipWithPrefix;
                            }
                        }
                    }
                }
            }

            process.waitFor();
            log.warn("Could not find eth1 IP address using ip command");

        } catch (Exception e) {
            log.error("Error getting eth1 IP address", e);
        }

        return null;
    }
}
