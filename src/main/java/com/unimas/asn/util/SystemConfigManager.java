package com.unimas.asn.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 系统配置管理工具类
 * 用于管理网卡配置、端口配置和应用配置文件
 */
public class SystemConfigManager {
    private static final Logger logger = LoggerFactory.getLogger(SystemConfigManager.class);
    
    private static final String APPLICATION_PROPERTIES_PATH = "/etc/unimas/tomcat/conf/application.properties";
    private static final String NETWORK_INTERFACES_PATH = "/etc/network/interfaces";
    private static final String NETWORK_INTERFACES_BACKUP = "/etc/network/interfaces.backup";
    private static final String RULE_SCRIPT_PATH = "/etc/unimas/tomcat/conf/rule.sh";


    
    /**
     * 获取当前监听端口（从配置文件读取）
     * @return 当前监听端口
     */
    public static int getCurrentManagementPort() {
        try (InputStream is = new FileInputStream(APPLICATION_PROPERTIES_PATH)) {
            Properties props = new Properties();
            props.load(is);
            String portStr = props.getProperty("server.port", "8080");
            return Integer.parseInt(portStr);
        } catch (Exception e) {
            logger.warn("Failed to read server.port from application.properties, using default 8080", e);
            return 8080;
        }
    }

    /**
     * 获取当前监听地址（从配置文件读取）
     * @return 当前监听地址
     */
    public static String getCurrentManagementAddress() {
        try (InputStream is = new FileInputStream(APPLICATION_PROPERTIES_PATH)) {
            Properties props = new Properties();
            props.load(is);
            return props.getProperty("server.address", "0.0.0.0");
        } catch (Exception e) {
            logger.warn("Failed to read server.address from application.properties, using default 0.0.0.0", e);
            return "0.0.0.0";
        }
    }
    
    /**
     * 从application.properties文件读取上位机配置
     * @return 包含centerIP和centerPort的Properties对象
     */
    public static Properties getCurrentCenterConfig() {
        Properties props = new Properties();
        try (InputStream is = new FileInputStream(APPLICATION_PROPERTIES_PATH)) {
            props.load(is);
        } catch (Exception e) {
            logger.error("Failed to read application.properties", e);
            // 设置默认值
            props.setProperty("alarm.report.destination.ip", "127.0.0.1");
            props.setProperty("alarm.report.destination.port", "8080");
        }
        return props;
    }
    
    /**
     * 修改eth0网卡的完整网络配置（Debian系统）
     * @param ip IP地址
     * @param subnetMask 子网掩码
     * @param gateway 网关（可选）
     * @return 是否修改成功
     */
    public static boolean updateEth0NetworkConfig(String ip, String subnetMask, String gateway) {
        try {
            logger.info("Updating eth0 network config - IP: {}, SubnetMask: {}, Gateway: {}",
                    ip, subnetMask, gateway);

            // 备份原始配置文件
            Files.copy(Paths.get(NETWORK_INTERFACES_PATH),
                      Paths.get(NETWORK_INTERFACES_BACKUP),
                      StandardCopyOption.REPLACE_EXISTING);

            // 读取当前配置文件
            String content = new String(Files.readAllBytes(Paths.get(NETWORK_INTERFACES_PATH)));

            // 更新eth0配置
            String updatedContent = updateNetworkInterfacesContentWithGateway(content, "eth0", ip, subnetMask, gateway);

            // 写入新配置
            Files.write(Paths.get(NETWORK_INTERFACES_PATH), updatedContent.getBytes());

            // 重启eth0网络接口以应用配置
            boolean restartSuccess = restartNetworkInterface("eth0", ip, subnetMask);
            if (!restartSuccess) {
                logger.warn("Failed to restart eth0 interface, configuration will take effect after reboot");
            }

            // 如果有网关配置，尝试手动应用网关（确保网关生效）
            if (gateway != null && !gateway.isEmpty()) {
                boolean gatewaySuccess = applyGatewayManually(gateway, "eth0");
                if (!gatewaySuccess) {
                    logger.warn("Failed to apply gateway manually, gateway may not be effective until reboot");
                }
            }

            logger.info("Successfully updated eth0 network configuration to persistent storage");
            return true;

        } catch (Exception e) {
            logger.error("Failed to update eth0 network configuration", e);
            // 尝试恢复备份
            try {
                Files.copy(Paths.get(NETWORK_INTERFACES_BACKUP),
                          Paths.get(NETWORK_INTERFACES_PATH),
                          StandardCopyOption.REPLACE_EXISTING);
            } catch (Exception restoreEx) {
                logger.error("Failed to restore network interfaces backup", restoreEx);
            }
            return false;
        }
    }
    /**
     * 修改eth1网卡的完整网络配置（Debian系统）
     * @param ip IP地址
     * @param subnetMask 子网掩码
     * @param gateway 网关（可选）
     * @return 是否修改成功
     */
    public static boolean updateEth1NetworkConfig(String ip, String subnetMask, String gateway) {
        try {
            logger.info("Updating eth1 network config - IP: {}, SubnetMask: {}, Gateway: {}",
                    ip, subnetMask, gateway);

            // 备份原始配置文件
            Files.copy(Paths.get(NETWORK_INTERFACES_PATH),
                      Paths.get(NETWORK_INTERFACES_BACKUP),
                      StandardCopyOption.REPLACE_EXISTING);

            // 读取当前配置文件
            String content = new String(Files.readAllBytes(Paths.get(NETWORK_INTERFACES_PATH)));

            // 更新eth1配置
            String updatedContent = updateNetworkInterfacesContentWithGateway(content, "eth1", ip, subnetMask, gateway);

            // 写入新配置
            Files.write(Paths.get(NETWORK_INTERFACES_PATH), updatedContent.getBytes());

            // 重启eth1网络接口以应用配置
            boolean restartSuccess = restartNetworkInterface("eth1", ip, subnetMask);
            if (!restartSuccess) {
                logger.warn("Failed to restart eth1 interface, configuration will take effect after reboot");
            }

            // 如果有网关配置，尝试手动应用网关（确保网关生效）
            if (gateway != null && !gateway.isEmpty()) {
                boolean gatewaySuccess = applyGatewayManually(gateway, "eth1");
                if (!gatewaySuccess) {
                    logger.warn("Failed to apply gateway manually, gateway may not be effective until reboot");
                }
            }//这里重启网卡就不用手动删路由了
            //TODO 管理ip rule规则
            // 管理ip rule规则
            boolean ruleSuccess = manageIpRule(ip, gateway, subnetMask, "business", "eth1");
            if (!ruleSuccess) {
                logger.warn("Failed to manage ip rule configuration, but continuing with network setup");
            }
            logger.info("Successfully updated eth1 network configuration to persistent storage");
            return true;

        } catch (Exception e) {
            logger.error("Failed to update eth1 network configuration", e);
            // 尝试恢复备份
            try {
                Files.copy(Paths.get(NETWORK_INTERFACES_BACKUP),
                          Paths.get(NETWORK_INTERFACES_PATH),
                          StandardCopyOption.REPLACE_EXISTING);
            } catch (Exception restoreEx) {
                logger.error("Failed to restore network interfaces backup", restoreEx);
            }
            return false;
        }
    }



    /**
     * 更新eth0虚地址配置到网络配置文件（Debian系统）
     * @param virtualIP 虚地址IP
     * @param subnetMask 子网掩码
     * @param gateway 网关（可选）
     * @return 是否修改成功
     */
    public static boolean updateEth0VirtualAddressConfig(String virtualIP, String subnetMask, String gateway) {
        try {
            logger.info("Updating eth0 virtual address config - IP: {}, SubnetMask: {}, Gateway: {}",
                    virtualIP, subnetMask, gateway);

            // 备份原始配置文件
            Files.copy(Paths.get(NETWORK_INTERFACES_PATH),
                      Paths.get(NETWORK_INTERFACES_BACKUP),
                      StandardCopyOption.REPLACE_EXISTING);

            // 读取当前配置文件
            String content = new String(Files.readAllBytes(Paths.get(NETWORK_INTERFACES_PATH)));

            // 读取当前eth0接口的网关配置
            String currentGateway = getCurrentGatewayFromConfig(content, "eth0");
            logger.info("Current eth0 gateway configuration: {}", currentGateway);

            // 更新eth0虚地址配置
            String updatedContent = updateEth0VirtualAddressContent(content, virtualIP, subnetMask, gateway);

            // 写入新配置
            Files.write(Paths.get(NETWORK_INTERFACES_PATH), updatedContent.getBytes());

            // 重启eth0和eth0:1接口以应用配置（特别是网关配置）
            boolean restartSuccess = restartEth0WithVirtualInterface(virtualIP, subnetMask);
            if (!restartSuccess) {
                logger.warn("Failed to restart eth0 interfaces, configuration will take effect after reboot");
            }

            // 处理网关配置变更
            if (gateway != null && !gateway.isEmpty()) {
                // 如果请求中有网关，应用新的网关
                boolean gatewaySuccess = applyGatewayManually(gateway, "eth0");
                if (!gatewaySuccess) {
                    logger.warn("Failed to apply gateway manually, gateway may not be effective until reboot");
                }
            } else if (currentGateway != null && !currentGateway.isEmpty()) {
                // 如果请求中没有网关，但当前配置中有网关，则删除老的网关
                logger.info("Removing existing gateway configuration: {}", currentGateway);
                boolean removeSuccess = removeGatewayManually(currentGateway, "eth0");
                if (!removeSuccess) {
                    logger.warn("Failed to remove existing gateway manually, old gateway may still be active");
                }
            }

            // 管理ip rule规则
            boolean ruleSuccess = manageIpRule(virtualIP, gateway, subnetMask, "manager", "eth0");
            if (!ruleSuccess) {
                logger.warn("Failed to manage ip rule configuration, but continuing with network setup");
            }

            logger.info("Successfully updated eth0 virtual address configuration to persistent storage");
            return true;

        } catch (Exception e) {
            logger.error("Failed to update eth0 virtual address configuration", e);
            // 尝试恢复备份
            try {
                Files.copy(Paths.get(NETWORK_INTERFACES_BACKUP),
                          Paths.get(NETWORK_INTERFACES_PATH),
                          StandardCopyOption.REPLACE_EXISTING);
            } catch (Exception restoreEx) {
                logger.error("Failed to restore network interfaces backup", restoreEx);
            }
            return false;
        }
    }




    /**
     * 更新eth0虚地址配置文件内容
     * @param content 原始内容
     * @param virtualIP 虚地址IP
     * @param subnetMask 子网掩码
     * @param gateway 网关（null表示不修改现有网关配置，空字符串表示移除网关，网关将配置在eth0主接口上）
     * @return 更新后的内容
     */
    private static String updateEth0VirtualAddressContent(String content, String virtualIP, String subnetMask, String gateway) {
        String[] lines = content.split("\n");
        StringBuilder result = new StringBuilder();
        boolean foundEth0Virtual = false;
        boolean shouldUpdateGateway = (gateway != null); // 只要gateway参数不为null就需要处理网关配置
        boolean hasGatewayValue = (gateway != null && !gateway.isEmpty()); // 是否有实际的网关值

        // 如果需要处理网关配置，只清除eth0接口的网关配置
        if (shouldUpdateGateway) {
            StringBuilder tempResult = new StringBuilder();
            boolean inEth0Section = false;

            for (String line : lines) {
                if (line.trim().startsWith("iface eth0 ") && !line.contains("eth0:")) {
                    inEth0Section = true;
                    tempResult.append(line).append("\n");
                } else if (inEth0Section && line.trim().startsWith("iface") && !line.contains("eth0")) {
                    // 进入其他接口配置段
                    inEth0Section = false;
                    tempResult.append(line).append("\n");
                } else if (inEth0Section && line.trim().isEmpty()) {
                    // 遇到空行，结束eth0配置段
                    inEth0Section = false;
                    tempResult.append(line).append("\n");
                } else if (inEth0Section && (line.trim().startsWith("gateway") || line.trim().startsWith("metric"))) {
                    // 跳过eth0接口的网关配置和metric参数
                    continue;
                } else {
                    tempResult.append(line).append("\n");
                }
            }
            // 重新解析清理后的内容
            content = tempResult.toString();
            lines = content.split("\n");
        }

        // 先移除现有的eth0:1配置
        boolean inEth0VirtualSection = false;
        for (String line : lines) {
            if (line.trim().startsWith("auto eth0:1") || line.trim().startsWith("iface eth0:1")) {
                inEth0VirtualSection = true;
                foundEth0Virtual = true;
                // 跳过现有的eth0:1配置
                continue;
            } else if (inEth0VirtualSection && line.trim().startsWith("iface") && !line.contains("eth0:1")) {
                // 遇到其他接口配置，结束eth0:1配置段
                inEth0VirtualSection = false;
                result.append(line).append("\n");
            } else if (inEth0VirtualSection && line.trim().isEmpty()) {
                // 遇到空行，结束eth0:1配置段
                inEth0VirtualSection = false;
                result.append(line).append("\n");
            } else if (inEth0VirtualSection) {
                // 跳过eth0:1配置段中的所有行
                continue;
            } else {
                result.append(line).append("\n");
            }
        }

        // 处理eth0主接口配置，确保存在且在需要时添加网关
        boolean hasEth0Main = false;
        boolean inEth0MainSection = false;
        boolean gatewayAddedToEth0 = false;
        String[] processedLines = result.toString().split("\n");
        result = new StringBuilder();

        for (String line : processedLines) {
            if (line.trim().startsWith("iface eth0 ") && !line.contains("eth0:")) {
                hasEth0Main = true;
                inEth0MainSection = true;
                result.append(line).append("\n");
            } else if (inEth0MainSection && line.trim().startsWith("address")) {
                // 在eth0主接口的address行后添加网关（如果有网关值）
                result.append(line).append("\n");
                if (hasGatewayValue && !gatewayAddedToEth0) {
                    result.append("gateway ").append(gateway).append("\n");
                    result.append("metric 201").append("\n");
                    gatewayAddedToEth0 = true;
                }
            } else if (inEth0MainSection && line.trim().startsWith("iface") && !line.contains("eth0")) {
                // 遇到其他接口配置，结束eth0配置段
                inEth0MainSection = false;
                result.append(line).append("\n");
            } else if (inEth0MainSection && line.trim().isEmpty()) {
                // 遇到空行，结束eth0配置段
                inEth0MainSection = false;
                result.append(line).append("\n");
            } else {
                result.append(line).append("\n");
            }
        }

        // 如果eth0主接口不存在，添加基本配置
        if (!hasEth0Main) {
            result.append("\n");
            result.append("auto eth0\n");
            result.append("iface eth0 inet dhcp\n");
            if (hasGatewayValue) {
                result.append("gateway ").append(gateway).append("\n");
                result.append("metric 201").append("\n");
                gatewayAddedToEth0 = true;
            }
        }

        // 如果有网关值但还没有添加到eth0，在eth0配置末尾添加
        if (hasGatewayValue && !gatewayAddedToEth0) {
            // 找到eth0配置的位置并添加网关
            String[] finalLines = result.toString().split("\n");
            result = new StringBuilder();
            inEth0MainSection = false;

            for (int i = 0; i < finalLines.length; i++) {
                String line = finalLines[i];
                if (line.trim().startsWith("iface eth0 ") && !line.contains("eth0:")) {
                    inEth0MainSection = true;
                    result.append(line).append("\n");
                } else if (inEth0MainSection && (line.trim().startsWith("iface") && !line.contains("eth0") || line.trim().isEmpty())) {
                    // 在eth0配置段结束前添加网关
                    result.append("gateway ").append(gateway).append("\n");
                    result.append("metric 201").append("\n");
                    result.append(line).append("\n");
                    inEth0MainSection = false;
                } else {
                    result.append(line).append("\n");
                }
            }
        }

        // 添加新的eth0:1虚地址配置（不包含网关）
        result.append("\n");
        result.append("auto eth0:1\n");
        result.append("iface eth0:1 inet static\n");
        result.append("address ").append(virtualIP).append("\n");
        result.append("netmask ").append(subnetMask).append("\n");

        return result.toString();
    }


    /**
     * 更新网络接口配置文件内容（包含网关）
     * @param content 原始内容
     * @param interfaceName 网卡名称（eth0或eth1）
     * @param ip IP地址
     * @param subnetMask 子网掩码
     * @param gateway 网关（null表示不修改现有网关配置，空字符串表示移除网关）
     * @return 更新后的内容
     */
    private static String updateNetworkInterfacesContentWithGateway(String content, String interfaceName, String ip, String subnetMask, String gateway) {
        String[] lines = content.split("\n");
        StringBuilder result = new StringBuilder();
        boolean inTargetSection = false;
        boolean foundTargetInterface = false;
        boolean shouldUpdateGateway = (gateway != null); // 只要gateway参数不为null就需要处理网关配置
        boolean hasGatewayValue = (gateway != null && !gateway.isEmpty()); // 是否有实际的网关值

        // 如果需要处理网关配置，只清除目标接口的网关配置
        if (shouldUpdateGateway) {
            boolean inTargetInterfaceSection = false;

            for (String line : lines) {
                if (line.trim().startsWith("iface " + interfaceName)) {
                    inTargetInterfaceSection = true;
                    result.append(line).append("\n");
                } else if (inTargetInterfaceSection && line.trim().startsWith("iface") && !line.contains(interfaceName)) {
                    // 进入其他接口配置段
                    inTargetInterfaceSection = false;
                    result.append(line).append("\n");
                } else if (inTargetInterfaceSection && line.trim().isEmpty()) {
                    // 遇到空行，结束目标接口配置段
                    inTargetInterfaceSection = false;
                    result.append(line).append("\n");
                } else if (inTargetInterfaceSection && line.trim().startsWith("gateway")) {
                    // 跳过目标接口的网关配置
                    continue;
                } else {
                    result.append(line).append("\n");
                }
            }
            // 重新解析清理后的内容
            content = result.toString();
            lines = content.split("\n");
            result = new StringBuilder();
        }

        inTargetSection = false;
        foundTargetInterface = false;

        for (String line : lines) {
            if (line.trim().startsWith("iface " + interfaceName)) {
                inTargetSection = true;
                foundTargetInterface = true;
                result.append(line).append("\n");
            } else if (inTargetSection && line.trim().startsWith("address")) {
                // 更新IP地址
                result.append("address ").append(ip).append("\n");
                // 添加子网掩码
                result.append("netmask ").append(subnetMask).append("\n");
                // 添加网关（仅当有网关值时）
                if (hasGatewayValue) {
                    result.append("gateway ").append(gateway).append("\n");
                }

                // 继续处理该接口的其他配置行，但跳过address、netmask、gateway和route相关配置
                continue;
            } else if (inTargetSection && line.trim().startsWith("netmask")) {
                // 跳过旧的netmask配置，我们已经重新添加了
                continue;
            } else if (inTargetSection && line.trim().startsWith("gateway")) {
                // 如果需要处理网关配置就跳过旧的gateway配置，否则保留
                if (shouldUpdateGateway) {
                    continue;
                } else {
                    // 保留现有的网关配置
                    result.append(line).append("\n");
                }

            } else if (inTargetSection && line.trim().startsWith("iface") && !line.contains(interfaceName)) {
                inTargetSection = false;
                result.append(line).append("\n");
            } else if (inTargetSection && line.trim().isEmpty()) {
                // 遇到空行，表示接口配置结束
                inTargetSection = false;
                result.append(line).append("\n");
            } else {
                result.append(line).append("\n");
            }
        }

        // 如果没有找到目标接口，添加新的接口配置
        if (!foundTargetInterface) {
            result.append("\n");
            result.append("auto ").append(interfaceName).append("\n");
            result.append("iface ").append(interfaceName).append(" inet static\n");
            result.append("address ").append(ip).append("\n");
            result.append("netmask ").append(subnetMask).append("\n");
            if (hasGatewayValue) {
                result.append("gateway ").append(gateway).append("\n");
            }
        }

        return result.toString();
    }


    /**
     * 重启网络接口以应用配置
     * @param interfaceName 网络接口名称（如eth0、eth1）
     * @param ip IP地址
     * @param subnetMask 子网掩码
     * @return 是否重启成功
     */
    private static boolean restartNetworkInterface(String interfaceName, String ip, String subnetMask) {
        try {
            logger.info("Restarting network interface: {} with IP: {} and netmask: {}", interfaceName, ip, subnetMask);

            // 先关闭接口
            ProcessBuilder pbDown = new ProcessBuilder("ifconfig", interfaceName, "down");
            Process processDown = pbDown.start();
            int exitCodeDown = processDown.waitFor();

            if (exitCodeDown != 0) {
                logger.warn("ifconfig {} down returned exit code: {}, continuing with configuration", interfaceName, exitCodeDown);
            }

            // 等待一秒
            Thread.sleep(1000);

            // 直接使用ifconfig配置IP地址、子网掩码并启动接口
            ProcessBuilder pbUp = new ProcessBuilder("ifconfig", interfaceName, ip, "netmask", subnetMask, "up");
            Process processUp = pbUp.start();
            int exitCodeUp = processUp.waitFor();

            if (exitCodeUp == 0) {
                logger.info("Successfully restarted and configured network interface: {} with IP: {} and netmask: {}", interfaceName, ip, subnetMask);
                return true;
            } else {
                logger.error("Failed to restart and configure network interface {}, ifconfig exit code: {}", interfaceName, exitCodeUp);
                return false;
            }

        } catch (Exception e) {
            logger.error("Error restarting network interface: {}", interfaceName, e);
            return false;
        }
    }

    /**
     * 重启eth0主接口和虚拟接口以应用配置
     * @param virtualIP 虚拟接口IP
     * @param subnetMask 虚拟接口子网掩码
     * @return 是否重启成功
     */
    private static boolean restartEth0WithVirtualInterface(String virtualIP, String subnetMask) {
        try {
            logger.info("Restarting eth0 and eth0:1 interfaces to apply configuration");

            // 先关闭虚拟接口
            ProcessBuilder pbDownVirtual = new ProcessBuilder("ifconfig", "eth0:1", "down");
            Process processDownVirtual = pbDownVirtual.start();
            processDownVirtual.waitFor();

            // 重启eth0主接口（这会应用网关配置）
//            boolean eth0Success = restartNetworkInterface("eth0");
//            if (!eth0Success) {
//                logger.warn("Failed to restart eth0, but continuing with virtual interface setup");
//            }

            // 等待一秒
            Thread.sleep(1000);

            ProcessBuilder pbManualUp = new ProcessBuilder("ifconfig", "eth0:1", virtualIP, "netmask", subnetMask, "up");
            Process processManualUp = pbManualUp.start();
            int exitCodeManualUp = processManualUp.waitFor();

            if (exitCodeManualUp == 0) {
                logger.info("Successfully configured virtual interface eth0:1 manually with IP {} and netmask {}", virtualIP, subnetMask);

                // 管理eth0:1虚拟接口的kernel路由
                boolean kernelRouteSuccess = manageKernelRoutes("eth0", virtualIP, subnetMask);
                if (!kernelRouteSuccess) {
                    logger.warn("Failed to manage kernel routes for eth0:1 virtual interface, but interface configuration was successful");
                }

                return true;
            } else {
                logger.error("Failed to configure virtual interface eth0:1 manually, ifconfig exit code: {}", exitCodeManualUp);
                return false;
            }
        } catch (Exception e) {
            logger.error("Error restarting eth0 with virtual interface", e);
            return false;
        }
    }

    /**
     * 手动应用网关配置（当ifup/ifdown不能正确处理时的备用方案）
     * @param gateway 网关IP地址
     * @param interfaceName 网络接口名称（如eth0、eth1）
     * @return 是否应用成功
     */
    private static boolean applyGatewayManually(String gateway, String interfaceName) {
        try {
            logger.info("Manually applying gateway configuration: {} via interface: {}", gateway, interfaceName);

            // 先检查当前路由状态
            logger.info("Checking current routing table before applying gateway...");
            executeCommandWithOutput("ip", "route", "show");

            // 删除现有的默认路由 - 使用ip route命令
            logger.info("Attempting to delete existing default routes...");
            ProcessBuilder pbDelRoute = new ProcessBuilder("bash", "-c","ip route del default via " + gateway + " dev " + interfaceName );
            Process processDelRoute = pbDelRoute.start();
            int exitCodeDel = processDelRoute.waitFor();
            logger.info("Delete default route command exit code: {}", exitCodeDel);

            // 等待一秒确保删除操作完成
            Thread.sleep(1000);

            // 添加新的默认路由 - 使用ip route add命令
            String addRouteCommand = "ip route add default via " + gateway + " dev " + interfaceName + ("eth0".equals(interfaceName)? " metric 201" :"");
            logger.info("Executing command: {}", addRouteCommand);

            ProcessBuilder pbAddRoute = new ProcessBuilder("bash", "-c", addRouteCommand);
            Process processAddRoute = pbAddRoute.start();

            // 读取命令输出
            String output = readProcessOutput(processAddRoute);
            int exitCodeAdd = processAddRoute.waitFor();

            logger.info("Add route command exit code: {}, output: {}", exitCodeAdd, output);

            if (exitCodeAdd == 0) {
                // 验证路由是否真的添加成功
                Thread.sleep(500);
                logger.info("Verifying gateway configuration after manual application...");
                boolean verified = verifyGatewayConfiguration(gateway);

                if (verified) {
                    logger.info("Successfully applied and verified gateway configuration manually: {} via {}", gateway, interfaceName);
                    return true;
                } else {
                    logger.error("Gateway command succeeded but verification failed for gateway: {} via {}", gateway, interfaceName);
                    return false;
                }
            } else {
                logger.error("Failed to add default route manually, exit code: {}, output: {}", exitCodeAdd, output);
                return false;
            }

        } catch (Exception e) {
            logger.error("Error applying gateway configuration manually", e);
            return false;
        }
    }
    /**
     * 读取进程输出
     */
    private static String readProcessOutput(Process process) {
        try {
            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }
            }
            try (BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()))) {
                String line;
                while ((line = errorReader.readLine()) != null) {
                    output.append("ERROR: ").append(line).append("\n");
                }
            }
            return output.toString().trim();
        } catch (Exception e) {
            logger.error("Error reading process output", e);
            return "";
        }
    }

    /**
     * 从配置文件内容中读取指定接口的当前网关配置
     * @param content 配置文件内容
     * @param interfaceName 接口名称（如eth0、eth1）
     * @return 当前网关IP，如果没有配置则返回null
     */
    private static String getCurrentGatewayFromConfig(String content, String interfaceName) {
        try {
            String[] lines = content.split("\n");
            boolean inTargetSection = false;

            for (String line : lines) {
                String trimmedLine = line.trim();

                if (trimmedLine.startsWith("iface " + interfaceName + " ") && !trimmedLine.contains(interfaceName + ":")) {
                    inTargetSection = true;
                } else if (inTargetSection && trimmedLine.startsWith("iface") && !trimmedLine.contains(interfaceName)) {
                    // 进入其他接口配置段，结束目标接口段
                    inTargetSection = false;
                } else if (inTargetSection && trimmedLine.isEmpty()) {
                    // 遇到空行，结束目标接口段
                    inTargetSection = false;
                } else if (inTargetSection && trimmedLine.startsWith("gateway ")) {
                    // 找到网关配置行
                    String[] parts = trimmedLine.split("\\s+");
                    if (parts.length >= 2) {
                        String gatewayIP = parts[1];
                        logger.info("Found current gateway for {}: {}", interfaceName, gatewayIP);
                        return gatewayIP;
                    }
                }
            }

            logger.info("No gateway configuration found for interface: {}", interfaceName);
            return null;

        } catch (Exception e) {
            logger.error("Error reading current gateway configuration for interface: {}", interfaceName, e);
            return null;
        }
    }

    /**
     * 手动删除网关配置
     * @param gateway 要删除的网关IP地址
     * @param interfaceName 网络接口名称（如eth0、eth1）
     * @return 是否删除成功
     */
    private static boolean removeGatewayManually(String gateway, String interfaceName) {
        try {
            logger.info("Manually removing gateway configuration: {} from interface: {}", gateway, interfaceName);

            // 先检查当前路由状态
            logger.info("Checking current routing table before removing gateway...");
            executeCommandWithOutput("ip", "route", "show");

            // 删除指定的默认路由
            logger.info("Attempting to delete default route via gateway: {}", gateway);
            ProcessBuilder pbDelRoute = new ProcessBuilder("bash", "-c", "ip route del default via " + gateway + " dev " + interfaceName);
            Process processDelRoute = pbDelRoute.start();
            int exitCodeDel = processDelRoute.waitFor();
            logger.info("Delete default route command exit code: {}", exitCodeDel);

            // 验证路由是否真的删除成功
            Thread.sleep(500);
            logger.info("Verifying gateway removal...");
            boolean verified = !verifyGatewayConfiguration(gateway);

            if (verified) {
                logger.info("Successfully removed gateway configuration: {} from {}", gateway, interfaceName);
                return true;
            } else {
                logger.warn("Gateway removal command completed but gateway may still be active: {} via {}", gateway, interfaceName);
                return false;
            }

        } catch (Exception e) {
            logger.error("Error removing gateway configuration", e);
            return false;
        }
    }

    /**
     * 验证网关配置是否生效
     */
    private static boolean verifyGatewayConfiguration(String expectedGateway) {
        try {
            ProcessBuilder pb = new ProcessBuilder("ip", "route", "show");
            Process process = pb.start();

            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    logger.info("Route table line: {}", line);
                    // 检查默认路由行，格式如：default via *********** dev eth0
                    if (line.trim().startsWith("default") && line.contains("via " + expectedGateway)) {
                        logger.info("Found expected default route with gateway: {}", expectedGateway);
                        return true;
                    }
                }
            }

            int exitCode = process.waitFor();
            logger.warn("Gateway verification failed - expected gateway {} not found in routing table, ip route command exit code: {}", expectedGateway, exitCode);
            return false;

        } catch (Exception e) {
            logger.error("Error verifying gateway configuration", e);
            return false;
        }
    }

    /**
     * 执行命令并输出结果到日志
     */
    private static void executeCommandWithOutput(String... command) {
        try {
            ProcessBuilder pb = new ProcessBuilder(command);
            Process process = pb.start();

            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    logger.info("Command output: {}", line);
                }
            }

            int exitCode = process.waitFor();
            logger.info("Command {} exit code: {}", String.join(" ", command), exitCode);

        } catch (Exception e) {
            logger.error("Error executing command with output: {}", String.join(" ", command), e);
        }
    }

    /**
     * 管理ip rule规则
     * @param virtualIP 虚拟IP地址
     * @param gateway 网关地址（如果没有网关就是IP地址）
     * @param subnetMask 子网掩码
     * @param tableName 路由表名称（如manager、business）
     * @param deviceName 设备名称（如eth0、eth1）
     * @return 是否成功
     */
    private static boolean manageIpRule(String virtualIP, String gateway, String subnetMask, String tableName, String deviceName) {
        try {
            logger.info("Managing ip rule configuration for virtual IP: {}, gateway: {}, subnetMask: {}, table: {}, device: {}", 
                    virtualIP, gateway, subnetMask, tableName, deviceName);
            
            // 确定路由目标地址：如果有网关就用网关，否则用IP地址
            String routeTarget = (gateway != null && !gateway.isEmpty()) ? gateway : virtualIP;
            
            // 计算CIDR格式的网络地址
            String cidrNetwork = calculateCidrNetwork(virtualIP, subnetMask);
            
            // 1. 先删除指定表的旧规则，确保一致性
            logger.info("Clearing existing {} table rules from rule.sh", tableName);
            
            // 从rule.sh文件中读取需要删除的规则
            List<String> rulesToDelete = getRulesToDelete(tableName);
            
            // 执行删除命令
            for (String rule : rulesToDelete) {
                if (rule.startsWith("ip rule add")) {
                    // 将add替换为del
                    String deleteRule = rule.replace("ip rule add", "ip rule del");
                    logger.info("Executing delete command: {}", deleteRule);
                    
                    ProcessBuilder pb = new ProcessBuilder("bash", "-c", deleteRule + " 2>/dev/null || true");
                    Process process = pb.start();
                    int exitCode = process.waitFor();
                    logger.info("Delete rule command exit code: {}", exitCode);
                    
                } else if (rule.startsWith("ip route add")) {
                    // 将add替换为del
                    String deleteRoute = rule.replace("ip route add", "ip route del");
                    logger.info("Executing delete command: {}", deleteRoute);
                    
                    ProcessBuilder pb = new ProcessBuilder("bash", "-c", deleteRoute + " 2>/dev/null || true");
                    Process process = pb.start();
                    int exitCode = process.waitFor();
                    logger.info("Delete route command exit code: {}", exitCode);
                }
            }
            
            // 等待一秒确保删除操作完成
            Thread.sleep(1000);
            
            // 2. 执行 ip rule add from x.x.x.x table {tableName}
            logger.info("Executing: ip rule add from {} table {}", virtualIP, tableName);
            ProcessBuilder pbRule = new ProcessBuilder("ip", "rule", "add", "from", virtualIP, "table", tableName);
            Process processRule = pbRule.start();
            int exitCodeRule = processRule.waitFor();
            
            if (exitCodeRule != 0) {
                logger.warn("ip rule add command returned exit code: {}, but continuing", exitCodeRule);
            }
            
            // 3. 执行 ip route add ip/MASK dev {deviceName} table {tableName}
            logger.info("Executing: ip route add {} dev {} table {}", cidrNetwork, deviceName, tableName);
            ProcessBuilder pbRouteNetwork = new ProcessBuilder("ip", "route", "add", cidrNetwork, "dev", deviceName, "table", tableName);
            Process processRouteNetwork = pbRouteNetwork.start();
            int exitCodeRouteNetwork = processRouteNetwork.waitFor();
            
            if (exitCodeRouteNetwork != 0) {
                logger.warn("ip route add network command returned exit code: {}, but continuing", exitCodeRouteNetwork);
            }
            
            // 4. 执行 ip route add default via X.X.X.X table {tableName}
            logger.info("Executing: ip route add default via {} table {}", routeTarget, tableName);
            ProcessBuilder pbRoute = new ProcessBuilder("ip", "route", "add", "default", "via", routeTarget, "table", tableName);
            Process processRoute = pbRoute.start();
            int exitCodeRoute = processRoute.waitFor();
            
            if (exitCodeRoute != 0) {
                logger.warn("ip route add default command returned exit code: {}, but continuing", exitCodeRoute);
            }
            
            // 5. 记录到rule.sh文件
            boolean scriptSuccess = updateRuleScript(virtualIP, routeTarget, cidrNetwork, tableName, deviceName);
            if (!scriptSuccess) {
                logger.error("Failed to update rule.sh script");
                return false;
            }
            
            // 6. 验证配置是否生效
            boolean verifySuccess = verifyIpRuleConfiguration(virtualIP, routeTarget, tableName);
            if (!verifySuccess) {
                logger.warn("IP rule configuration verification failed, but configuration has been applied");
            }
            
            logger.info("Successfully managed ip rule configuration");
            return true;
            
        } catch (Exception e) {
            logger.error("Error managing ip rule configuration", e);
            return false;
        }
    }
    
    /**
     * 更新rule.sh脚本文件
     * @param virtualIP 虚拟IP地址
     * @param routeTarget 路由目标地址
     * @param cidrNetwork 网络地址
     * @param tableName 路由表名称（如manager、business）
     * @param deviceName 设备名称（如eth0、eth1）
     * @return 是否成功
     */
    private static boolean updateRuleScript(String virtualIP, String routeTarget, String cidrNetwork, String tableName, String deviceName) {
        try {
            logger.info("Updating rule.sh script with virtual IP: {}, route target: {}, cidrNetwork: {}, table: {}, device: {}", 
                    virtualIP, routeTarget, cidrNetwork, tableName, deviceName);
            
            Path scriptPath = Paths.get(RULE_SCRIPT_PATH);
            
            // 确保目录存在
            Files.createDirectories(scriptPath.getParent());
            
            // 读取现有脚本内容，保留其他表的规则
            List<String> existingLines = new ArrayList<>();
            if (Files.exists(scriptPath)) {
                try {
                    List<String> lines = Files.readAllLines(scriptPath);
                    for (String line : lines) {
                        // 跳过当前表的旧规则和注释
                        if (line.startsWith("#!/bin/bash") || 
                            line.trim().isEmpty() ||
                            (line.contains("table " + tableName)) ||
                            (line.startsWith("# Rules for table " + tableName))) {
                            continue;
                        }
                        // 保留其他表的规则
                        if (line.startsWith("ip rule add") || line.startsWith("ip route add") || line.startsWith("# Rules for table")) {
                            existingLines.add(line);
                        }
                    }
                } catch (Exception e) {
                    logger.warn("Failed to read existing rule.sh file, will create new one", e);
                }
            }
            
            // 创建新的脚本内容
            StringBuilder scriptContent = new StringBuilder();
            scriptContent.append("#!/bin/bash\n\n");
            
            // 添加现有其他表的规则
            for (String line : existingLines) {
                scriptContent.append(line).append("\n");
            }
            
            // 如果有其他表的规则，添加空行分隔
            if (!existingLines.isEmpty()) {
                scriptContent.append("\n");
            }
            
            // 添加当前表的新规则
            scriptContent.append("# Rules for table ").append(tableName).append("\n");
            scriptContent.append("ip rule add from ").append(virtualIP).append(" table ").append(tableName).append("\n");
            scriptContent.append("ip route add ").append(cidrNetwork).append(" dev ").append(deviceName).append(" table ").append(tableName).append("\n");
            scriptContent.append("ip route add default via ").append(routeTarget).append(" table ").append(tableName).append("\n");
            
            // 写入脚本内容
            Files.write(scriptPath, scriptContent.toString().getBytes());
            
            // 设置执行权限
            try {
                ProcessBuilder pb = new ProcessBuilder("chmod", "+x", RULE_SCRIPT_PATH);
                Process process = pb.start();
                process.waitFor();
            } catch (Exception e) {
                logger.warn("Failed to set execute permission on rule.sh, but script was created successfully", e);
            }
            
            logger.info("Successfully updated rule.sh script at: {}", RULE_SCRIPT_PATH);
            return true;
            
        } catch (Exception e) {
            logger.error("Error updating rule.sh script", e);
            return false;
        }
    }

    /**
     * 验证ip rule规则是否生效
     * @param virtualIP 虚拟IP地址
     * @param routeTarget 路由目标地址
     * @param tableName 路由表名称（如manager、business）
     * @return 是否成功
     */
    private static boolean verifyIpRuleConfiguration(String virtualIP, String routeTarget, String tableName) {
        try {
            ProcessBuilder pb = new ProcessBuilder("ip", "rule", "show");
            Process process = pb.start();

            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    logger.info("Rule table line: {}", line);
                    // 检查规则行，格式如：from *********** table business
                    if (line.trim().startsWith("from " + virtualIP) && line.contains("table " + tableName)) {
                        logger.info("Found expected rule for IP: {}", virtualIP);
                        return true;
                    }
                }
            }

            pb = new ProcessBuilder("ip", "route", "show");
            process = pb.start();

            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    logger.info("Route table line: {}", line);
                    // 检查默认路由行，格式如：default via *********** dev eth0
                    if (line.trim().startsWith("default") && line.contains("via " + routeTarget)) {
                        logger.info("Found expected default route with route target: {}", routeTarget);
                        return true;
                    }
                }
            }

            int exitCode = process.waitFor();
            logger.warn("IP rule verification failed - expected rule for IP {} not found in rule table, ip rule command exit code: {}", virtualIP, exitCode);
            return false;

        } catch (Exception e) {
            logger.error("Error verifying ip rule configuration", e);
            return false;
        }
    }

    /**
     * 计算CIDR格式的网络地址
     * @param ip IP地址
     * @param subnetMask 子网掩码
     * @return CIDR格式的网络地址（如***********/24）
     */
    private static String calculateCidrNetwork(String ip, String subnetMask) {
        try {
            String[] ipParts = ip.split("\\.");
            String[] maskParts = subnetMask.split("\\.");
            
            // 计算网络地址
            StringBuilder networkAddress = new StringBuilder();
            for (int i = 0; i < 4; i++) {
                int ipPart = Integer.parseInt(ipParts[i]);
                int maskPart = Integer.parseInt(maskParts[i]);
                networkAddress.append(ipPart & maskPart);
                if (i < 3) {
                    networkAddress.append(".");
                }
            }
            
            // 计算前缀长度
            int prefixLength = 0;
            for (String maskPart : maskParts) {
                int mask = Integer.parseInt(maskPart);
                prefixLength += Integer.bitCount(mask);
            }
            
            return networkAddress.toString() + "/" + prefixLength;
        } catch (Exception e) {
            logger.error("Error calculating CIDR network for IP: {}, mask: {}", ip, subnetMask, e);
            // 返回默认值
            return ip + "/24";
        }
    }

    /**
     * 从rule.sh文件中读取需要删除的规则
     * @param tableName 路由表名称（如manager、business）
     * @return 需要删除的规则列表
     */
    private static List<String> getRulesToDelete(String tableName) {
        List<String> rulesToDelete = new ArrayList<>();
        try {
            List<String> lines = Files.readAllLines(Paths.get(RULE_SCRIPT_PATH));
            for (String line : lines) {
                if (line.contains("table " + tableName)) {
                    rulesToDelete.add(line);
                }
            }
        } catch (Exception e) {
            logger.warn("Failed to read rule.sh file, will not delete any rules", e);
        }
        return rulesToDelete;
    }

    /**
     * 管理kernel路由（处理proto kernel scope link src类型路由）
     * @param interfaceName 网络接口名称（如eth0、eth1）
     * @param ip IP地址
     * @param subnetMask 子网掩码
     * @return 是否成功
     */
    private static boolean manageKernelRoutes(String interfaceName, String ip, String subnetMask) {
        try {
            logger.info("Managing kernel routes for interface: {} with IP: {} and netmask: {}", interfaceName, ip, subnetMask);

            // 计算CIDR格式的网络地址
            String cidrNetwork = calculateCidrNetwork(ip, subnetMask);

            // 1. 删除现有的kernel路由
            boolean deleteSuccess = deleteExistingKernelRoute(interfaceName, cidrNetwork, ip);
            if (!deleteSuccess) {
                logger.warn("Failed to delete existing kernel route, but continuing with adding new route");
            }

            // 等待一秒确保删除操作完成
            Thread.sleep(1000);

            // 2. 添加新的kernel路由（带metric参数）
            boolean addSuccess = addKernelRouteWithMetric(interfaceName, cidrNetwork, ip);
            if (!addSuccess) {
                logger.error("Failed to add new kernel route with metric");
                return false;
            }

            logger.info("Successfully managed kernel routes for interface: {}", interfaceName);
            return true;

        } catch (Exception e) {
            logger.error("Error managing kernel routes for interface: {}", interfaceName, e);
            return false;
        }
    }

    /**
     * 删除现有的kernel路由
     * @param interfaceName 网络接口名称
     * @param cidrNetwork CIDR格式的网络地址
     * @param sourceIp 源IP地址
     * @return 是否成功
     */
    private static boolean deleteExistingKernelRoute(String interfaceName, String cidrNetwork, String sourceIp) {
        try {
            logger.info("Deleting existing kernel route for network: {} dev {} src {}", cidrNetwork, interfaceName, sourceIp);

            // 构建删除命令：ip route del NETWORK/MASK dev INTERFACE proto kernel scope link src IP
            String deleteCommand = String.format("ip route del %s dev %s proto kernel scope link src %s",
                                                cidrNetwork, interfaceName, sourceIp);

            logger.info("Executing delete command: {}", deleteCommand);
            ProcessBuilder pb = new ProcessBuilder("bash", "-c", deleteCommand + " 2>/dev/null || true");
            Process process = pb.start();
            int exitCode = process.waitFor();

            if (exitCode == 0) {
                logger.info("Successfully deleted existing kernel route");
                return true;
            } else {
                logger.warn("Delete kernel route command returned exit code: {}, route may not exist", exitCode);
                return true; // 路由不存在也算成功
            }

        } catch (Exception e) {
            logger.error("Error deleting existing kernel route", e);
            return false;
        }
    }

    /**
     * 添加带metric的kernel路由
     * @param interfaceName 网络接口名称
     * @param cidrNetwork CIDR格式的网络地址
     * @param sourceIp 源IP地址
     * @return 是否成功
     */
    private static boolean addKernelRouteWithMetric(String interfaceName, String cidrNetwork, String sourceIp) {
        try {
            logger.info("Adding kernel route with metric for network: {} dev {} src {}", cidrNetwork, interfaceName, sourceIp);

            // 构建添加命令：ip route add NETWORK/MASK dev INTERFACE proto kernel scope link src IP metric 201
            String addCommand = String.format("ip route add %s dev %s proto kernel scope link src %s metric 201",
                                             cidrNetwork, interfaceName, sourceIp);

            logger.info("Executing add command: {}", addCommand);
            ProcessBuilder pb = new ProcessBuilder("bash", "-c", addCommand);
            Process process = pb.start();

            // 读取命令输出
            String output = readProcessOutput(process);
            int exitCode = process.waitFor();

            if (exitCode == 0) {
                logger.info("Successfully added kernel route with metric 201");

                // 验证路由是否真的添加成功
                Thread.sleep(500);
                boolean verified = verifyKernelRoute(interfaceName, cidrNetwork, sourceIp);
                if (verified) {
                    logger.info("Kernel route verification successful");
                    return true;
                } else {
                    logger.warn("Kernel route added but verification failed");
                    return false;
                }
            } else {
                logger.error("Failed to add kernel route with metric, exit code: {}, output: {}", exitCode, output);
                return false;
            }

        } catch (Exception e) {
            logger.error("Error adding kernel route with metric", e);
            return false;
        }
    }

    /**
     * 验证kernel路由是否存在
     * @param interfaceName 网络接口名称
     * @param cidrNetwork CIDR格式的网络地址
     * @param sourceIp 源IP地址
     * @return 是否存在
     */
    private static boolean verifyKernelRoute(String interfaceName, String cidrNetwork, String sourceIp) {
        try {
            ProcessBuilder pb = new ProcessBuilder("ip", "route", "show");
            Process process = pb.start();

            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    logger.debug("Route table line: {}", line);
                    // 检查是否包含期望的路由：NETWORK dev INTERFACE proto kernel scope link src IP metric 201
                    if (line.contains(cidrNetwork) &&
                        line.contains("dev " + interfaceName) &&
                        line.contains("proto kernel") &&
                        line.contains("scope link") &&
                        line.contains("src " + sourceIp) &&
                        line.contains("metric 201")) {
                        logger.info("Found expected kernel route with metric 201: {}", line);
                        return true;
                    }
                }
            }

            int exitCode = process.waitFor();
            logger.warn("Kernel route verification failed - expected route not found, ip route command exit code: {}", exitCode);
            return false;

        } catch (Exception e) {
            logger.error("Error verifying kernel route", e);
            return false;
        }
    }
}
