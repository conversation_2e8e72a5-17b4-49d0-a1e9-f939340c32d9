package com.unimas.asn.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.w3c.dom.*;
import javax.xml.parsers.*;
import javax.xml.transform.*;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.File;
import java.io.RandomAccessFile;
import java.nio.channels.FileChannel;
import java.nio.channels.FileLock;
import java.util.HashMap;
import java.util.Map;

public class ConfigXmlOperator {
    private static final Logger log = LoggerFactory.getLogger(ConfigXmlOperator.class);
    private Document document;
    private String filePath;
    private final Object lockObject = new Object(); // 用于同步的对象

    public ConfigXmlOperator(String filePath) {
        this.filePath = filePath;
        File file = new File(filePath);
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            if (!file.exists()) {
                log.info("配置文件不存在，创建新的模板文件: {}", filePath);
                createTemplate();
                if (document == null) {
                    throw new RuntimeException("创建模板失败，document为null");
                }
            } else {
                log.info("加载现有配置文件: {}", filePath);
                // 检查文件是否为空
                if (file.length() == 0) {
                    log.warn("配置文件为空，重新创建模板: {}", filePath);
                    createTemplate();
                } else {
                    document = builder.parse(file);
                    if (document == null) {
                        throw new RuntimeException("解析XML文件失败，document为null");
                    }
                }
            }
        } catch (Exception e) {
            log.error("初始化ConfigXmlOperator失败，文件路径: " + filePath, e);
            // 尝试创建备份文件名
            String backupPath = filePath + ".backup." + System.currentTimeMillis();
            if (file.exists() && file.length() > 0) {
                try {
                    java.nio.file.Files.copy(file.toPath(), new File(backupPath).toPath());
                    log.info("已备份损坏的配置文件到: {}", backupPath);
                } catch (Exception backupException) {
                    log.error("备份文件失败", backupException);
                }
            }
            // 尝试重新创建模板
            try {
                createTemplate();
            } catch (Exception templateException) {
                log.error("重新创建模板也失败", templateException);
                throw new RuntimeException("无法初始化配置文件操作器", templateException);
            }
        }
    }

    // 添加新节点
    public void addNode(String parentNodeName, String newNodeName, Map<String, String> attributes) {
        if (document == null) {
            log.error("无法添加节点，document为null");
            return;
        }
        try {
            NodeList nodeList = document.getElementsByTagName("node");
            for (int i = 0; i < nodeList.getLength(); i++) {
                Node node = nodeList.item(i);
                if (node.getAttributes().getNamedItem("name").getNodeValue().equals(parentNodeName)) {
                    Element newNode = document.createElement("node");
                    newNode.setAttribute("name", newNodeName);

                    Element mapElement = document.createElement("map");
                    for (Map.Entry<String, String> entry : attributes.entrySet()) {
                        Element entryElement = document.createElement("entry");
                        entryElement.setAttribute("key", entry.getKey());
                        entryElement.setAttribute("value", entry.getValue());
                        mapElement.appendChild(entryElement);
                    }

                    newNode.appendChild(mapElement);
                    node.appendChild(newNode);
                    saveXml();
                    break;
                }
            }
        } catch (Exception e) {
            log.error("添加节点失败，父节点: " + parentNodeName + ", 新节点: " + newNodeName, e);
        }
    }

    // 删除节点
    public void deleteNode(String nodeName) {
        if (document == null) {
            log.error("无法删除节点，document为null");
            return;
        }
        try {
            NodeList nodeList = document.getElementsByTagName("node");
            for (int i = 0; i < nodeList.getLength(); i++) {
                Node node = nodeList.item(i);
                if (node.getAttributes().getNamedItem("name").getNodeValue().equals(nodeName)) {
                    node.getParentNode().removeChild(node);
                    saveXml();
                    break;
                }
            }
        } catch (Exception e) {
            log.error("删除节点失败，节点名: " + nodeName, e);
        }
    }

    /**
     * 检查是否存在指定的displayname
     * @param displayName 要检查的显示名称
     * @return 如果存在相同displayname返回true，否则返回false
     */
    public boolean isDisplayNameExist(String displayName) {
        if (document == null) {
            log.error("无法检查displayname，document为null");
            return false;
        }
        try {
            NodeList nodeList = document.getElementsByTagName("node");
            for (int i = 0; i < nodeList.getLength(); i++) {
                Node node = nodeList.item(i);
                NodeList mapNodes = ((Element) node).getElementsByTagName("map");
                if (mapNodes.getLength() > 0) {
                    Element mapElement = (Element) mapNodes.item(0);
                    NodeList entries = mapElement.getElementsByTagName("entry");
                    for (int j = 0; j < entries.getLength(); j++) {
                        Element entry = (Element) entries.item(j);
                        if (entry.getAttribute("key").equals("displayname") &&
                            entry.getAttribute("value").equals(displayName)) {
                            return true;
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("检查displayname是否存在时发生错误", e);
        }
        return false;
    }

    // 修改节点属性
    public void updateNodeAttribute(String nodeName, String key, String value) {
        if (document == null) {
            log.error("无法更新节点属性，document为null");
            return;
        }
        try {
            NodeList nodeList = document.getElementsByTagName("node");
            for (int i = 0; i < nodeList.getLength(); i++) {
                Node node = nodeList.item(i);
                if (node.getAttributes().getNamedItem("name").getNodeValue().equals(nodeName)) {
                    NodeList mapNodes = ((Element) node).getElementsByTagName("map");
                    if (mapNodes.getLength() > 0) {
                        Element mapElement = (Element) mapNodes.item(0);
                        NodeList entries = mapElement.getElementsByTagName("entry");
                        boolean found = false;
                        for (int j = 0; j < entries.getLength(); j++) {
                            Element entry = (Element) entries.item(j);
                            if (entry.getAttribute("key").equals(key)) {
                                entry.setAttribute("value", value);
                                found = true;
                                break;
                            }
                        }
                        if (!found) {
                            Element newEntry = document.createElement("entry");
                            newEntry.setAttribute("key", key);
                            newEntry.setAttribute("value", value);
                            mapElement.appendChild(newEntry);
                        }
                        saveXml();
                    }
                    break;
                }
            }
        } catch (Exception e) {
            log.error("更新节点属性失败，节点: " + nodeName + ", 属性: " + key, e);
        }
    }

    // 保存XML文件（改进版本，使用文件锁）
    private void saveXml() {
        if (document == null) {
            log.error("无法保存XML，document为null");
            return;
        }
        
        synchronized (lockObject) {
            // 创建临时文件进行写入
            String tempFilePath = filePath + ".tmp";
            File tempFile = new File(tempFilePath);
            File originalFile = new File(filePath);
            RandomAccessFile raf = null;
            FileChannel channel = null;
            FileLock lock = null;
            
            try {
                TransformerFactory transformerFactory = TransformerFactory.newInstance();
                Transformer transformer = transformerFactory.newTransformer();
                transformer.setOutputProperty(OutputKeys.INDENT, "yes");
                transformer.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "2");
                transformer.setOutputProperty(OutputKeys.STANDALONE, "no");
                DOMSource source = new DOMSource(document);
                StreamResult result = new StreamResult(tempFile);
                transformer.transform(source, result);
                
                // 检查临时文件是否写入成功且不为空
                if (tempFile.exists() && tempFile.length() > 0) {
                    // 获取文件锁进行原子性替换
                    try {
                        if (originalFile.exists()) {
                            raf = new RandomAccessFile(originalFile, "rw");
                            channel = raf.getChannel();
                            lock = channel.tryLock();
                            if (lock != null) {
                                originalFile.delete();
                            } else {
                                log.warn("无法获取文件锁，可能存在并发访问: {}", filePath);
                                // 等待一段时间后重试
                                Thread.sleep(100);
                                originalFile.delete();
                            }
                        }
                        
                        if (!tempFile.renameTo(originalFile)) {
                            // 如果重命名失败，尝试复制
                            java.nio.file.Files.copy(tempFile.toPath(), originalFile.toPath(), 
                                java.nio.file.StandardCopyOption.REPLACE_EXISTING);
                            tempFile.delete();
                        }
                        log.debug("XML文件保存成功: {}", filePath);
                    } finally {
                        // 释放资源
                        if (lock != null) {
                            try {
                                lock.release();
                            } catch (Exception e) {
                                log.warn("释放文件锁失败", e);
                            }
                        }
                        if (channel != null) {
                            try {
                                channel.close();
                            } catch (Exception e) {
                                log.warn("关闭文件通道失败", e);
                            }
                        }
                        if (raf != null) {
                            try {
                                raf.close();
                            } catch (Exception e) {
                                log.warn("关闭RandomAccessFile失败", e);
                            }
                        }
                    }
                } else {
                    log.error("临时文件写入失败或为空: {}", tempFilePath);
                    if (tempFile.exists()) {
                        tempFile.delete();
                    }
                }
            } catch (Exception e) {
                log.error("保存XML文件失败: " + filePath, e);
                // 清理临时文件
                if (tempFile.exists()) {
                    tempFile.delete();
                }
            }
        }
    }

    private void createTemplate() {
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            document = builder.newDocument();

            if (document == null) {
                throw new RuntimeException("无法创建新的Document对象");
            }

            // 创建根元素
            Element preferences = document.createElement("preferences");
            preferences.setAttribute("EXTERNAL_XML_VERSION", "1.0");
            document.appendChild(preferences);

            // 创建root节点
            Element root = document.createElement("root");
            root.setAttribute("type", "system");
            preferences.appendChild(root);

            // 添加空的map节点
            Element rootMap = document.createElement("map");
            root.appendChild(rootMap);

            // 创建apps节点
            Element apps = document.createElement("node");
            apps.setAttribute("name", "apps");
            root.appendChild(apps);

            // 添加apps的map节点
            Element appsMap = document.createElement("map");
            apps.appendChild(appsMap);
            addEntry(appsMap, "servicenum", "80");
            addEntry(appsMap, "servicecount", "0");
            addEntry(appsMap, "isSrc", "true");
            addEntry(appsMap, "appcount", "BTM0_app1");
            addEntry(appsMap, "channelcount", "0");

            // 创建BTM0_app1节点
            Element btmApp = document.createElement("node");
            btmApp.setAttribute("name", "BTM0_app1");
            apps.appendChild(btmApp);

            // 添加BTM0_app1的map节点
            Element btmMap = document.createElement("map");
            btmApp.appendChild(btmMap);
            addEntry(btmMap, "departmentName", "unimas");
            addEntry(btmMap, "competentPhone", "123456");
            addEntry(btmMap, "isImport", "false");
            addEntry(btmMap, "competentName", "unimas");
            addEntry(btmMap, "displayname", "unimas");

            saveXml();
            log.info("XML模板创建成功");
        } catch (Exception e) {
            log.error("创建XML模板失败", e);
            document = null; // 确保失败时document为null
            throw new RuntimeException("创建XML模板失败", e);
        }
    }

    private void addEntry(Element mapElement, String key, String value) {
        Element entry = document.createElement("entry");
        entry.setAttribute("key", key);
        entry.setAttribute("value", value);
        mapElement.appendChild(entry);
    }

    // 示例使用方法
    public static void main(String[] args) {
        ConfigXmlOperator operator = new ConfigXmlOperator("config.xml");

        // 添加新节点示例
        Map<String, String> attributes = new HashMap<>();
        attributes.put("creator", "unimas");
        attributes.put("isAudit", "true");
        attributes.put("type", "11");
        attributes.put("servicetype", "tcp");
        attributes.put("displayname", "newService");

        operator.addNode("BTM0_app1", "newNode", attributes);

        // 修改节点属性示例
        operator.updateNodeAttribute("11", "displayname", "updatedName");

        // 删除节点示例
        operator.deleteNode("newNode");
    }

    private int getMaxNodeNumber() {
        if (document == null) {
            log.error("无法获取最大节点编号，document为null");
            return 10; // 返回默认值
        }
        int maxNumber = 10;
        NodeList nodeList = document.getElementsByTagName("node");
        for (int i = 0; i < nodeList.getLength(); i++) {
            Node node = nodeList.item(i);
            String nodeName = node.getAttributes().getNamedItem("name").getNodeValue();
            try {
                int number = Integer.parseInt(nodeName);
                if (number > maxNumber) {
                    maxNumber = number;
                }
            } catch (NumberFormatException e) {
                // 忽略非数字节点名
            }
        }
        return maxNumber;
    }

    public int add(Map<String, String> attributes) {
        if (document == null) {
            log.error("无法添加服务，document为null");
            return -1; // 返回错误码
        }
        try {
            int newNodeNumber = getMaxNodeNumber() + 1;
            attributes.put("scheduleid", newNodeNumber + "");
            addNode("BTM0_app1", String.valueOf(newNodeNumber), attributes);
            return newNodeNumber;
        } catch (Exception e) {
            log.error("添加服务失败", e);
            return -1;
        }
    }

    /**
     * 获取指定节点的isRun属性值
     * @param sid 节点名称
     * @return isRun属性的值，如果节点不存在或属性不存在则返回false
     */
    public boolean getIsRun(String sid) {
        if (document == null) {
            log.error("无法获取isRun属性，document为null");
            return false;
        }
        try {
            NodeList nodeList = document.getElementsByTagName("node");
            for (int i = 0; i < nodeList.getLength(); i++) {
                Node node = nodeList.item(i);
                if (node.getAttributes().getNamedItem("name").getNodeValue().equals(sid)) {
                    NodeList mapNodes = ((Element) node).getElementsByTagName("map");
                    if (mapNodes.getLength() > 0) {
                        Element mapElement = (Element) mapNodes.item(0);
                        NodeList entries = mapElement.getElementsByTagName("entry");
                        for (int j = 0; j < entries.getLength(); j++) {
                            Element entry = (Element) entries.item(j);
                            if (entry.getAttribute("key").equals("isRun")) {
                                return Boolean.parseBoolean(entry.getAttribute("value"));
                            }
                        }
                    }
                    break;
                }
            }
        } catch (Exception e) {
            log.error("获取isRun属性时发生错误", e);

        }
        return false;
    }

    /**
     * 设置指定节点的isRun属性值
     * @param sid 节点名称
     * @param value 要设置的值
     */
    public void setIsRun(String sid, boolean value) {
        updateNodeAttribute(sid, "isRun", String.valueOf(value));
    }

    /**
     * 检查指定服务ID是否存在
     * @param serviceId 服务ID（即节点名称）
     * @return 如果服务存在返回true，否则返回false
     */
    public boolean isServiceExist(String serviceId) {
        if (document == null) {
            log.error("无法检查服务是否存在，document为null");
            return false;
        }
        try {
            NodeList nodeList = document.getElementsByTagName("node");
            for (int i = 0; i < nodeList.getLength(); i++) {
                Node node = nodeList.item(i);
                if (node.getAttributes().getNamedItem("name") != null &&
                    node.getAttributes().getNamedItem("name").getNodeValue().equals(serviceId)) {
                    // 检查该节点是否是BTM0_app1的子节点，以确认它是一个服务
                    Node parentNode = node.getParentNode();
                    if (parentNode != null && parentNode.getNodeType() == Node.ELEMENT_NODE) {
                        Element parentElement = (Element) parentNode;
                        if (parentElement.getAttribute("name").equals("BTM0_app1")) {
                            return true;
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("检查服务是否存在时发生错误", e);
        }
        return false;
    }

    /**
     * 获取所有服务ID
     * @return 服务ID列表
     */
    public java.util.List<Integer> getAllServiceIds() {
        java.util.List<Integer> serviceIds = new java.util.ArrayList<>();
        if (document == null) {
            log.error("无法获取服务ID列表，document为null");
            return serviceIds; // 返回空列表
        }
        try {
            NodeList nodes = document.getElementsByTagName("node");
            for (int i = 0; i < nodes.getLength(); i++) {
                Node node = nodes.item(i);
                if (node.getNodeType() == Node.ELEMENT_NODE) {
                    Element element = (Element) node;
                    String nodeName = element.getAttribute("name");

                    // 检查是否为服务节点（数字形式的节点名称）
                    try {
                        int serviceId = Integer.parseInt(nodeName);
                        // 检查是否是BTM0_app1的子节点
                        Node parentNode = node.getParentNode();
                        if (parentNode != null && parentNode.getNodeType() == Node.ELEMENT_NODE) {
                            Element parentElement = (Element) parentNode;
                            if ("BTM0_app1".equals(parentElement.getAttribute("name"))) {
                                // 添加到服务ID列表
                                serviceIds.add(serviceId);
                            }
                        }
                    } catch (NumberFormatException e) {
                        // 忽略非数字节点名称
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取所有服务ID时发生错误", e);
        }
        return serviceIds;
    }

    /**
     * 验证XML文件的完整性
     * @return 如果文件完整返回true，否则返回false
     */
    public boolean validateXmlIntegrity() {
        if (document == null) {
            log.error("无法验证XML完整性，document为null");
            return false;
        }
        
        try {
            // 检查根元素是否存在
            Element root = document.getDocumentElement();
            if (root == null || !"preferences".equals(root.getNodeName())) {
                log.error("XML文件缺少preferences根元素");
                return false;
            }
            
            // 检查是否有root节点
            NodeList rootNodes = document.getElementsByTagName("root");
            if (rootNodes.getLength() == 0) {
                log.error("XML文件缺少root节点");
                return false;
            }
            
            // 检查是否有apps节点
            NodeList appsNodes = document.getElementsByTagName("node");
            boolean hasAppsNode = false;
            boolean hasBtmNode = false;
            
            for (int i = 0; i < appsNodes.getLength(); i++) {
                Node node = appsNodes.item(i);
                String nodeName = node.getAttributes().getNamedItem("name").getNodeValue();
                if ("apps".equals(nodeName)) {
                    hasAppsNode = true;
                } else if ("BTM0_app1".equals(nodeName)) {
                    hasBtmNode = true;
                }
            }
            
            if (!hasAppsNode) {
                log.error("XML文件缺少apps节点");
                return false;
            }
            
            if (!hasBtmNode) {
                log.error("XML文件缺少BTM0_app1节点");
                return false;
            }
            
            log.debug("XML文件完整性验证通过");
            return true;
            
        } catch (Exception e) {
            log.error("验证XML完整性时发生错误", e);
            return false;
        }
    }

    /**
     * 获取当前document状态
     * @return document是否可用
     */
    public boolean isDocumentAvailable() {
        return document != null;
    }
}