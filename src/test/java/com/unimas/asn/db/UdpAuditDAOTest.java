package com.unimas.asn.db;

import org.junit.Test;
import org.junit.Before;
import static org.junit.Assert.*;
import java.util.Map;

/**
 * Test class for UdpAuditDAO new methods
 */
public class UdpAuditDAOTest {
    
    private UdpAuditDAO udpAuditDAO;
    
    @Before
    public void setUp() {
        udpAuditDAO = new UdpAuditDAO();
    }
    
    @Test
    public void testGetSendStatsByServiceId() {
        // Test with a sample service ID
        String serviceId = "1";
        Map<String, Long> result = udpAuditDAO.getSendStatsByServiceId(serviceId);
        
        // Verify that the result contains the expected keys
        assertNotNull("Result should not be null", result);
        assertTrue("Result should contain totalCount key", result.containsKey("totalCount"));
        assertTrue("Result should contain totalBytes key", result.containsKey("totalBytes"));
        
        // Verify that values are not null (they should be 0 if no data exists)
        assertNotNull("totalCount should not be null", result.get("totalCount"));
        assertNotNull("totalBytes should not be null", result.get("totalBytes"));
        
        System.out.println("Send stats for service " + serviceId + ": " + result);
    }
    
    @Test
    public void testGetRecvStatsByServiceId() {
        // Test with a sample service ID
        String serviceId = "1";
        Map<String, Long> result = udpAuditDAO.getRecvStatsByServiceId(serviceId);
        
        // Verify that the result contains the expected keys
        assertNotNull("Result should not be null", result);
        assertTrue("Result should contain totalCount key", result.containsKey("totalCount"));
        assertTrue("Result should contain totalBytes key", result.containsKey("totalBytes"));
        
        // Verify that values are not null (they should be 0 if no data exists)
        assertNotNull("totalCount should not be null", result.get("totalCount"));
        assertNotNull("totalBytes should not be null", result.get("totalBytes"));
        
        System.out.println("Recv stats for service " + serviceId + ": " + result);
    }
}
